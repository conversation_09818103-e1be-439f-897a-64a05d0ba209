"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Plus, Search, MoreHorizontal, Edit, Trash2, Factory, Clock, Users, Zap } from "lucide-react";

// Sample data - in a real app, this would come from an API
const sampleWorkCenterData = [
  { 
    id: 1, 
    workCenterCode: "WC-001", 
    workCenterName: "Assembly Line 1", 
    department: "Assembly",
    capacity: 100,
    capacityUnit: "units/hour",
    operatingCost: 50.00,
    status: "Active",
    operators: 4,
    efficiency: 85
  },
  { 
    id: 2, 
    workCenterCode: "WC-002", 
    workCenterName: "CNC Machining Center", 
    department: "Machining",
    capacity: 25,
    capacityUnit: "parts/hour",
    operatingCost: 120.00,
    status: "Active",
    operators: 2,
    efficiency: 92
  },
  { 
    id: 3, 
    workCenterCode: "WC-003", 
    workCenterName: "Welding Station", 
    department: "Fabrication",
    capacity: 15,
    capacityUnit: "joints/hour",
    operatingCost: 75.00,
    status: "Active",
    operators: 3,
    efficiency: 78
  },
  { 
    id: 4, 
    workCenterCode: "WC-004", 
    workCenterName: "Quality Control Lab", 
    department: "Quality",
    capacity: 50,
    capacityUnit: "tests/hour",
    operatingCost: 40.00,
    status: "Active",
    operators: 2,
    efficiency: 95
  },
  { 
    id: 5, 
    workCenterCode: "WC-005", 
    workCenterName: "Packaging Line", 
    department: "Packaging",
    capacity: 200,
    capacityUnit: "units/hour",
    operatingCost: 30.00,
    status: "Active",
    operators: 3,
    efficiency: 88
  },
  { 
    id: 6, 
    workCenterCode: "WC-006", 
    workCenterName: "Old Assembly Line", 
    department: "Assembly",
    capacity: 80,
    capacityUnit: "units/hour",
    operatingCost: 45.00,
    status: "Inactive",
    operators: 0,
    efficiency: 0
  },
];

export default function WorkCentersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState("All");
  const [statusFilter, setStatusFilter] = useState("All");

  const filteredData = sampleWorkCenterData.filter(workCenter => {
    const matchesSearch = workCenter.workCenterName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workCenter.workCenterCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workCenter.department.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartment = departmentFilter === "All" || workCenter.department === departmentFilter;
    const matchesStatus = statusFilter === "All" || workCenter.status === statusFilter;
    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const handleDelete = (id: number) => {
    // In a real app, this would make an API call
    alert(`Delete work center with ID: ${id}`);
  };

  const departments = [...new Set(sampleWorkCenterData.map(item => item.department))];

  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 90) return 'text-green-600 bg-green-50';
    if (efficiency >= 80) return 'text-blue-600 bg-blue-50';
    if (efficiency >= 70) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Work Centers</h1>
            <p className="text-gray-600">
              Manage production workstations, their capacities, and operational parameters
            </p>
          </div>
          <Link href="/masters/manufacturing/work-centers/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Work Center
            </Button>
          </Link>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Factory className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Work Centers</p>
                  <p className="text-2xl font-bold">{sampleWorkCenterData.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Zap className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Centers</p>
                  <p className="text-2xl font-bold">
                    {sampleWorkCenterData.filter(wc => wc.status === "Active").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Users className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Operators</p>
                  <p className="text-2xl font-bold">
                    {sampleWorkCenterData.reduce((sum, wc) => sum + wc.operators, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Avg. Efficiency</p>
                  <p className="text-2xl font-bold">
                    {Math.round(sampleWorkCenterData.filter(wc => wc.status === "Active").reduce((sum, wc) => sum + wc.efficiency, 0) / sampleWorkCenterData.filter(wc => wc.status === "Active").length)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search work centers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Department: {departmentFilter}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setDepartmentFilter("All")}>All</DropdownMenuItem>
                  {departments.map((dept) => (
                    <DropdownMenuItem key={dept} onClick={() => setDepartmentFilter(dept)}>
                      {dept}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Status: {statusFilter}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setStatusFilter("All")}>All</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("Active")}>Active</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("Inactive")}>Inactive</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Export to Excel</DropdownMenuItem>
                  <DropdownMenuItem>Capacity Planning</DropdownMenuItem>
                  <DropdownMenuItem>Efficiency Report</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card className="frappe-table">
          <CardHeader>
            <CardTitle>Work Centers List ({filteredData.length} records)</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Work Center Code</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Capacity</TableHead>
                  <TableHead>Operating Cost</TableHead>
                  <TableHead>Operators</TableHead>
                  <TableHead>Efficiency</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((workCenter) => (
                  <TableRow key={workCenter.id}>
                    <TableCell className="font-medium">{workCenter.workCenterCode}</TableCell>
                    <TableCell>{workCenter.workCenterName}</TableCell>
                    <TableCell>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                        {workCenter.department}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">{workCenter.capacity}</div>
                        <div className="text-gray-500">{workCenter.capacityUnit}</div>
                      </div>
                    </TableCell>
                    <TableCell>₹{workCenter.operatingCost.toFixed(2)}/hr</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 text-gray-500" />
                        {workCenter.operators}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getEfficiencyColor(workCenter.efficiency)}`}>
                        {workCenter.efficiency}%
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        workCenter.status === 'Active' ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50'
                      }`}>
                        {workCenter.status}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/manufacturing/work-centers/${workCenter.id}`}>
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/manufacturing/work-centers/${workCenter.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            View Schedule
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            Capacity Analysis
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(workCenter.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No work centers found. {searchTerm && "Try adjusting your search terms."}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help Section */}
        <Card>
          <CardHeader>
            <CardTitle>Work Center Management</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Work Center Configuration</h4>
                <p>
                  Work centers represent physical or logical production units where operations are performed. 
                  Configure capacity, operating costs, and efficiency parameters for accurate production planning.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Key Metrics</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li><strong>Capacity:</strong> Maximum output per time unit</li>
                  <li><strong>Efficiency:</strong> Actual vs theoretical performance</li>
                  <li><strong>Operating Cost:</strong> Cost per hour of operation</li>
                  <li><strong>Operators:</strong> Required workforce</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
