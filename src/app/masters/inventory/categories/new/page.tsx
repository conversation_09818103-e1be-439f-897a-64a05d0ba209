"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import Link from "next/link";
import { ArrowLeft, Save } from "lucide-react";

export default function NewCategoryPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    isParentCategory: false,
    parentCategory: "",
    categoryName: "",
    categoryCode: "",
    description: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  // Sample parent categories for dropdown
  const parentCategories = [
    "Raw Materials",
    "Finished Goods", 
    "Semi-Finished",
    "Consumables",
    "Tools & Equipment"
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      alert("Category created successfully!");
      router.push("/masters/inventory/categories");
    }, 1000);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center gap-4">
          <Link href="/masters/inventory/categories">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Add New Category</h1>
            <p className="text-gray-600">
              Create a new item category to organize your inventory
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="max-w-2xl">
          <Card className="frappe-form">
            <CardHeader>
              <CardTitle>Category Details</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Parent Category Toggle */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      id="isParentCategory"
                      name="isParentCategory"
                      type="checkbox"
                      checked={formData.isParentCategory}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <Label htmlFor="isParentCategory">
                      Is this a Parent Category?
                    </Label>
                  </div>
                  <p className="text-sm text-gray-500">
                    Parent categories can contain sub-categories. Uncheck to create a sub-category.
                  </p>
                </div>

                {/* Parent Category Selection (only if not parent) */}
                {!formData.isParentCategory && (
                  <div className="space-y-2">
                    <Label htmlFor="parentCategory">
                      Select Parent Category <span className="text-red-500">*</span>
                    </Label>
                    <select
                      id="parentCategory"
                      name="parentCategory"
                      value={formData.parentCategory}
                      onChange={(e) => setFormData(prev => ({ ...prev, parentCategory: e.target.value }))}
                      className="flex h-10 w-full rounded-md border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                      required={!formData.isParentCategory}
                    >
                      <option value="">Select a parent category</option>
                      {parentCategories.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="categoryName">
                      Category Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="categoryName"
                      name="categoryName"
                      type="text"
                      placeholder="e.g., Raw Materials"
                      value={formData.categoryName}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="categoryCode">
                      Category Code
                    </Label>
                    <Input
                      id="categoryCode"
                      name="categoryCode"
                      type="text"
                      placeholder="e.g., RM"
                      value={formData.categoryCode}
                      onChange={handleInputChange}
                    />
                    <p className="text-sm text-gray-500">
                      Optional short code for the category
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Brief description of the category..."
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                  />
                </div>

                {/* Form Actions */}
                <div className="flex items-center gap-4 pt-6 border-t">
                  <Button type="submit" disabled={isLoading}>
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading ? "Saving..." : "Save Category"}
                  </Button>
                  <Link href="/masters/inventory/categories">
                    <Button variant="outline" type="button">
                      Cancel
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
