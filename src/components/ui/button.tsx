import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap text-sm font-normal border disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "sap-button-primary",
        destructive: "sap-button-primary",
        outline: "sap-button-secondary",
        secondary: "sap-button-secondary",
        ghost: "sap-button-secondary border-0",
        link: "sap-button-secondary border-0 underline",
      },
      size: {
        default: "",
        sm: "px-2 py-1 text-xs",
        lg: "px-4 py-2",
        icon: "p-1",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
