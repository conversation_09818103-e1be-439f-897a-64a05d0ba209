"use client";

import { ReactNode, useState, useEffect } from "react";
import { Sidebar } from "./sidebar";
import { Button } from "@/components/ui/button";
import { SiteSelectionPopup } from "@/components/site-selection-popup";
import { Bell, Search, User, MapPin } from "lucide-react";

interface DashboardLayoutProps {
  children: ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [companyName, setCompanyName] = useState('Default Company');
  const [siteName, setSiteName] = useState('Default Site');
  const [companyId, setCompanyId] = useState<number>(1);
  const [showSitePopup, setShowSitePopup] = useState(false);

  useEffect(() => {
    // Only access localStorage on the client side
    const savedCompanyName = localStorage.getItem('selectedCompanyName');
    const savedSiteName = localStorage.getItem('selectedSiteName');
    const savedCompanyId = localStorage.getItem('selectedCompanyId');

    if (savedCompanyName) {
      setCompanyName(savedCompanyName);
    }
    if (savedSiteName) {
      setSiteName(savedSiteName);
    }
    if (savedCompanyId) {
      setCompanyId(parseInt(savedCompanyId));
    }
  }, []);

  const handleSiteChange = () => {
    setShowSitePopup(true);
  };

  const handleSiteSelect = (siteId: number, newSiteName: string) => {
    try {
      console.log('Changing site to ID:', siteId, 'Name:', newSiteName);

      // Store selected site in localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('selectedSiteId', siteId.toString());
        localStorage.setItem('selectedSiteName', newSiteName);

        // Update local state
        setSiteName(newSiteName);

        console.log('Site changed successfully');

        // Refresh the page to update all components with new site context
        setTimeout(() => {
          window.location.reload();
        }, 100);
      }
    } catch (error) {
      console.error('Error changing site:', error);
      alert('Error changing site. Please try again.');
    }
  };

  const handleCloseSitePopup = () => {
    setShowSitePopup(false);
  };

  return (
    <div className="flex h-screen" style={{ backgroundColor: 'var(--sap-background)' }}>
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation - SAP Style */}
        <header className="sap-header">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 peer-focus:opacity-0 peer-[&:not(:placeholder-shown)]:opacity-0 transition-opacity" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="peer sap-input pl-12 pr-3 py-1 w-64 text-xs placeholder:text-gray-400"
                  style={{ fontSize: 'var(--sap-font-size-sm)', paddingLeft: '3rem' }}
                />
              </div>
            </div>

            <div className="flex items-center gap-1">
              <button className="sap-button-secondary p-1">
                <Bell className="h-3 w-3" />
              </button>
              <button className="sap-button-secondary p-1">
                <User className="h-3 w-3" />
              </button>
            </div>
          </div>
        </header>

        {/* SAP Menu Bar */}
        <div className="sap-menubar">
          <span className="sap-menu-item">File</span>
          <span className="sap-menu-item">Edit</span>
          <span className="sap-menu-item">View</span>
          <span className="sap-menu-item">Tools</span>
          <span className="sap-menu-item">System</span>
          <span className="sap-menu-item">Help</span>
        </div>

        {/* Toolbar - SAP Style */}
        <div className="sap-toolbar">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span style={{ fontSize: 'var(--sap-font-size-sm)', color: 'var(--sap-text-secondary)' }}>
                Production Management System
              </span>
            </div>
            <div className="flex items-center gap-1">
              <button
                className="sap-button-secondary px-2 py-1 text-xs flex items-center gap-1"
                onClick={handleSiteChange}
                title="Change Site"
              >
                <MapPin className="h-3 w-3" />
                {siteName}
              </button>
              <button className="sap-button-secondary px-2 py-1 text-xs">Help</button>
              <button className="sap-button-secondary px-2 py-1 text-xs">Settings</button>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto sap-main-content">
          <div style={{ padding: 'var(--sap-spacing-lg)' }}>
            {children}
          </div>
        </main>

        {/* SAP Status Bar */}
        <div style={{
          backgroundColor: 'var(--sap-surface-alt)',
          borderTop: '1px solid var(--sap-border)',
          padding: 'var(--sap-spacing-xs) var(--sap-spacing-md)',
          fontSize: 'var(--sap-font-size-xs)',
          color: 'var(--sap-text-secondary)',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <span>Ready</span>
          <span>User: Admin | Company: {companyName} | Site: {siteName}</span>
        </div>
      </div>

      {/* Site Selection Popup */}
      <SiteSelectionPopup
        isOpen={showSitePopup}
        onClose={handleCloseSitePopup}
        companyId={companyId}
        companyName={companyName}
        onSiteSelect={handleSiteSelect}
      />
    </div>
  );
}
