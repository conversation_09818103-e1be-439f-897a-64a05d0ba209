import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Home() {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: 'var(--sap-background)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 'var(--sap-spacing-xl)'
    }}>
      <div style={{ width: '100%', maxWidth: '400px' }}>
        <Card>
          <CardHeader style={{ textAlign: 'center' }}>
            <CardTitle>
              Production Management System
            </CardTitle>
            <CardDescription>
              Welcome to your production management platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div style={{ textAlign: 'center' }}>
              <p style={{
                fontSize: 'var(--sap-font-size-sm)',
                color: 'var(--sap-text-secondary)',
                margin: '0 0 16px 0'
              }}>
                Get started by logging in to your account or setting up a new one
              </p>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--sap-spacing-md)' }}>
                <Link href="/login" style={{ textDecoration: 'none' }}>
                  <Button style={{ width: '100%' }} size="lg">
                    Login to Account
                  </Button>
                </Link>
                <Link href="/setup" style={{ textDecoration: 'none' }}>
                  <Button variant="outline" style={{ width: '100%' }} size="lg">
                    Setup New Account
                  </Button>
                </Link>
              </div>
            </div>
            <div style={{
              paddingTop: 'var(--sap-spacing-lg)',
              borderTop: '1px solid var(--sap-border)',
              marginTop: 'var(--sap-spacing-lg)'
            }}>
              <p style={{
                fontSize: 'var(--sap-font-size-xs)',
                color: 'var(--sap-text-muted)',
                textAlign: 'center',
                margin: '0'
              }}>
                Built with modern web technologies for efficient production management
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
