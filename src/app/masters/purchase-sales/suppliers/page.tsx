"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Plus, Search, MoreHorizontal, Edit, Trash2, Building, Phone, Mail, MapPin } from "lucide-react";

// Sample data - in a real app, this would come from an API
const sampleSupplierData = [
  { 
    id: 1, 
    supplierCode: "SUP-001", 
    supplierName: "ABC Manufacturing Ltd", 
    contactPerson: "<PERSON>",
    phone: "+91 98765 43210",
    email: "<EMAIL>",
    city: "Mumbai",
    supplierType: "Raw Material",
    status: "Active",
    creditLimit: 500000,
    paymentTerms: "Net 30"
  },
  { 
    id: 2, 
    supplierCode: "SUP-002", 
    supplierName: "XYZ Components Inc", 
    contactPerson: "Sarah Johnson",
    phone: "+91 87654 32109",
    email: "<EMAIL>",
    city: "Delhi",
    supplierType: "Components",
    status: "Active",
    creditLimit: 750000,
    paymentTerms: "Net 45"
  },
  { 
    id: 3, 
    supplierCode: "SUP-003", 
    supplierName: "Steel Works Co", 
    contactPerson: "Mike Wilson",
    phone: "+91 76543 21098",
    email: "<EMAIL>",
    city: "Chennai",
    supplierType: "Raw Material",
    status: "Active",
    creditLimit: 1000000,
    paymentTerms: "Net 30"
  },
  { 
    id: 4, 
    supplierCode: "SUP-004", 
    supplierName: "Quality Tools Ltd", 
    contactPerson: "Lisa Davis",
    phone: "+91 65432 10987",
    email: "<EMAIL>",
    city: "Bangalore",
    supplierType: "Tools & Equipment",
    status: "Active",
    creditLimit: 300000,
    paymentTerms: "Net 15"
  },
  { 
    id: 5, 
    supplierCode: "SUP-005", 
    supplierName: "Packaging Solutions", 
    contactPerson: "David Brown",
    phone: "+91 54321 09876",
    email: "<EMAIL>",
    city: "Pune",
    supplierType: "Packaging",
    status: "Active",
    creditLimit: 200000,
    paymentTerms: "Net 30"
  },
  { 
    id: 6, 
    supplierCode: "SUP-006", 
    supplierName: "Old Supplier Co", 
    contactPerson: "Robert Taylor",
    phone: "+91 43210 98765",
    email: "<EMAIL>",
    city: "Kolkata",
    supplierType: "Raw Material",
    status: "Inactive",
    creditLimit: 100000,
    paymentTerms: "Net 30"
  },
];

export default function SuppliersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("All");
  const [statusFilter, setStatusFilter] = useState("All");

  const filteredData = sampleSupplierData.filter(supplier => {
    const matchesSearch = supplier.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.supplierCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.city.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === "All" || supplier.supplierType === typeFilter;
    const matchesStatus = statusFilter === "All" || supplier.status === statusFilter;
    return matchesSearch && matchesType && matchesStatus;
  });

  const handleDelete = (id: number) => {
    // In a real app, this would make an API call
    alert(`Delete supplier with ID: ${id}`);
  };

  const supplierTypes = [...new Set(sampleSupplierData.map(item => item.supplierType))];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Suppliers</h1>
            <p className="text-gray-600">
              Manage supplier information, contacts, and business relationships
            </p>
          </div>
          <Link href="/masters/purchase-sales/suppliers/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Supplier
            </Button>
          </Link>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Building className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Suppliers</p>
                  <p className="text-2xl font-bold">{sampleSupplierData.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Building className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Suppliers</p>
                  <p className="text-2xl font-bold">
                    {sampleSupplierData.filter(sup => sup.status === "Active").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <MapPin className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Cities</p>
                  <p className="text-2xl font-bold">
                    {new Set(sampleSupplierData.map(sup => sup.city)).size}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Building className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Credit Limit</p>
                  <p className="text-2xl font-bold">
                    ₹{(sampleSupplierData.reduce((sum, sup) => sum + sup.creditLimit, 0) / 1000000).toFixed(1)}M
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search suppliers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Type: {typeFilter}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setTypeFilter("All")}>All</DropdownMenuItem>
                  {supplierTypes.map((type) => (
                    <DropdownMenuItem key={type} onClick={() => setTypeFilter(type)}>
                      {type}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Status: {statusFilter}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setStatusFilter("All")}>All</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("Active")}>Active</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("Inactive")}>Inactive</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Export to Excel</DropdownMenuItem>
                  <DropdownMenuItem>Import from Excel</DropdownMenuItem>
                  <DropdownMenuItem>Supplier Performance Report</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card className="frappe-table">
          <CardHeader>
            <CardTitle>Suppliers List ({filteredData.length} records)</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Supplier Code</TableHead>
                  <TableHead>Supplier Name</TableHead>
                  <TableHead>Contact Person</TableHead>
                  <TableHead>Contact Info</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Credit Limit</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((supplier) => (
                  <TableRow key={supplier.id}>
                    <TableCell className="font-medium">{supplier.supplierCode}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-gray-500" />
                        {supplier.supplierName}
                      </div>
                    </TableCell>
                    <TableCell>{supplier.contactPerson}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1 text-sm">
                          <Phone className="h-3 w-3 text-gray-400" />
                          {supplier.phone}
                        </div>
                        <div className="flex items-center gap-1 text-sm">
                          <Mail className="h-3 w-3 text-gray-400" />
                          {supplier.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        {supplier.city}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                        {supplier.supplierType}
                      </span>
                    </TableCell>
                    <TableCell>₹{(supplier.creditLimit / 1000).toFixed(0)}K</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        supplier.status === 'Active' ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50'
                      }`}>
                        {supplier.status}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/purchase-sales/suppliers/${supplier.id}`}>
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/purchase-sales/suppliers/${supplier.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            Purchase History
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            Performance Report
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(supplier.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No suppliers found. {searchTerm && "Try adjusting your search terms."}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help Section */}
        <Card>
          <CardHeader>
            <CardTitle>Supplier Management Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Supplier Information</h4>
                <p>
                  Maintain comprehensive supplier profiles including contact details, business terms, 
                  and performance metrics. Regular updates ensure smooth procurement operations.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Best Practices</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li>Verify supplier credentials and certifications</li>
                  <li>Set appropriate credit limits based on business volume</li>
                  <li>Regular performance evaluation and feedback</li>
                  <li>Maintain backup suppliers for critical items</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
