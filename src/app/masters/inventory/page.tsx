import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { 
  Package, 
  Tags, 
  Ruler, 
  ArrowRightLeft, 
  Settings, 
  MapPin 
} from "lucide-react";

export default function InventoryMastersPage() {
  const inventoryMasters = [
    {
      title: "Item",
      description: "Manage all items in your inventory",
      icon: Package,
      href: "/masters/inventory/items",
      count: "1,234 items",
    },
    {
      title: "Item Category",
      description: "Organize items into categories",
      icon: Tags,
      href: "/masters/inventory/categories",
      count: "45 categories",
    },
    {
      title: "Unit of Measurement (UoM)",
      description: "Define units for measuring items",
      icon: Ruler,
      href: "/masters/inventory/uom",
      count: "23 units",
    },
    {
      title: "UoM Conversion Factor",
      description: "Set conversion rates between units",
      icon: ArrowRightLeft,
      href: "/masters/inventory/uom-conversion",
      count: "67 conversions",
    },
    {
      title: "Item Attributes",
      description: "Define custom attributes for items",
      icon: Settings,
      href: "/masters/inventory/attributes",
      count: "12 attributes",
    },
    {
      title: "Locations Master",
      description: "Manage storage locations",
      icon: MapPin,
      href: "/masters/inventory/locations",
      count: "8 locations",
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Inventory Masters</h1>
          <p className="text-gray-600">
            Manage all inventory-related master data including items, categories, units, and locations.
          </p>
        </div>

        {/* Masters Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {inventoryMasters.map((master) => (
            <Card key={master.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <master.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{master.title}</CardTitle>
                    <p className="text-sm text-gray-500">{master.count}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="mb-4">
                  {master.description}
                </CardDescription>
                <Link href={master.href}>
                  <Button className="w-full">
                    Manage {master.title}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks for inventory management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link href="/masters/inventory/items/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <Package className="h-6 w-6" />
                  <span className="text-sm">Add New Item</span>
                </Button>
              </Link>
              <Link href="/masters/inventory/categories/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <Tags className="h-6 w-6" />
                  <span className="text-sm">Add Category</span>
                </Button>
              </Link>
              <Link href="/masters/inventory/uom/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <Ruler className="h-6 w-6" />
                  <span className="text-sm">Add UoM</span>
                </Button>
              </Link>
              <Link href="/masters/inventory/locations/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <MapPin className="h-6 w-6" />
                  <span className="text-sm">Add Location</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
