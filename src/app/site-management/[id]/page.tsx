"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Edit, MapPin, Building2, Phone, Mail } from "lucide-react";

// Sample site data
const sampleSites = [
  { 
    id: 1, 
    siteName: "Main Manufacturing Plant", 
    gstNumber: "27ABCDE1234F1Z5", 
    country: "India", 
    state: "Maharashtra", 
    address: "Plot No. 123, Industrial Area, Pune", 
    pincode: "411001", 
    isDefault: true,
    isActive: true,
    companyName: "Acme Manufacturing Co.",
    contactPerson: "<PERSON>",
    phone: "+91 98765 43210",
    email: "<EMAIL>",
    createdDate: "2024-01-15"
  },
  { 
    id: 2, 
    siteName: "Warehouse - Mumbai", 
    gstNumber: "27ABCDE1234F2Z6", 
    country: "India", 
    state: "Maharashtra", 
    address: "Godown No. 45, MIDC, Mumbai", 
    pincode: "400001", 
    isDefault: false,
    isActive: true,
    companyName: "Acme Manufacturing Co.",
    contactPerson: "Jane Smith",
    phone: "+91 98765 43211",
    email: "<EMAIL>",
    createdDate: "2024-02-10"
  },
];

export default function SiteDetailsPage() {
  const params = useParams();
  const siteId = parseInt(params.id as string);
  
  const [site, setSite] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load site data
    const siteData = sampleSites.find(s => s.id === siteId);
    setSite(siteData);
    setIsLoading(false);
  }, [siteId]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading site details...</p>
        </div>
      </div>
    );
  }

  if (!site) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="max-w-md">
          <CardContent className="text-center py-8">
            <p className="text-red-600 mb-4">Site not found</p>
            <Link href="/site-management">
              <Button>Back to Site Management</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/site-management">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div className="flex items-center gap-3 flex-1">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MapPin className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{site.siteName}</h1>
              <p className="text-gray-600">Site Details</p>
            </div>
          </div>
          <Link href={`/site-management/${site.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              Edit Site
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Site Name</label>
                <p className="text-lg font-semibold">{site.siteName}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Company</label>
                <p className="text-lg">{site.companyName}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">GST Number</label>
                <p className="font-mono bg-gray-100 px-2 py-1 rounded text-sm">{site.gstNumber}</p>
              </div>
              
              <div className="flex gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Status</label>
                  <p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      site.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {site.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </p>
                </div>
                
                {site.isDefault && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Default Site</label>
                    <p>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Default
                      </span>
                    </p>
                  </div>
                )}
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Created Date</label>
                <p>{site.createdDate}</p>
              </div>
            </CardContent>
          </Card>

          {/* Location Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Location Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Address</label>
                <p className="text-lg">{site.address}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">State</label>
                  <p>{site.state}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">Country</label>
                  <p>{site.country}</p>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Pincode</label>
                <p className="font-mono">{site.pincode}</p>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Contact Person</label>
                <p className="text-lg">{site.contactPerson}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Phone</label>
                <p className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  {site.phone}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Email</label>
                <p className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  {site.email}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href={`/site-management/${site.id}/edit`} className="block">
                <Button variant="outline" className="w-full">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Site Details
                </Button>
              </Link>
              
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => {
                  // Set as selected site
                  localStorage.setItem('selectedSiteId', site.id.toString());
                  localStorage.setItem('selectedSiteName', site.siteName);
                  window.location.href = '/dashboard';
                }}
              >
                <Building2 className="h-4 w-4 mr-2" />
                Select This Site
              </Button>
              
              <Link href="/site-management" className="block">
                <Button variant="outline" className="w-full">
                  Back to Site Management
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
