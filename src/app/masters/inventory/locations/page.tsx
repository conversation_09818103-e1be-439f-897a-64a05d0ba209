"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Plus, Search, MoreHorizontal, Edit, Trash2, MapPin, Warehouse, Building } from "lucide-react";

// Sample data - in a real app, this would come from an API
const sampleLocationData = [
  { 
    id: 1, 
    locationCode: "WH-001", 
    locationName: "Main Warehouse", 
    locationType: "Warehouse",
    parentLocation: null,
    address: "123 Industrial Area, City",
    capacity: "10000 sq.ft",
    isActive: true,
    manager: "<PERSON>"
  },
  { 
    id: 2, 
    locationCode: "WH-001-A", 
    locationName: "Section A", 
    locationType: "Section",
    parentLocation: "Main Warehouse",
    address: "Main Warehouse - Section A",
    capacity: "2500 sq.ft",
    isActive: true,
    manager: "Jane Smith"
  },
  { 
    id: 3, 
    locationCode: "WH-001-B", 
    locationName: "Section B", 
    locationType: "Section",
    parentLocation: "Main Warehouse",
    address: "Main Warehouse - Section B",
    capacity: "2500 sq.ft",
    isActive: true,
    manager: "Mike Johnson"
  },
  { 
    id: 4, 
    locationCode: "WH-002", 
    locationName: "Secondary Warehouse", 
    locationType: "Warehouse",
    parentLocation: null,
    address: "456 Storage Lane, City",
    capacity: "5000 sq.ft",
    isActive: true,
    manager: "Sarah Wilson"
  },
  { 
    id: 5, 
    locationCode: "PROD-001", 
    locationName: "Production Floor", 
    locationType: "Production",
    parentLocation: null,
    address: "789 Manufacturing St, City",
    capacity: "8000 sq.ft",
    isActive: true,
    manager: "David Brown"
  },
  { 
    id: 6, 
    locationCode: "QC-001", 
    locationName: "Quality Control Lab", 
    locationType: "Quality Control",
    parentLocation: null,
    address: "101 Testing Ave, City",
    capacity: "500 sq.ft",
    isActive: true,
    manager: "Lisa Davis"
  },
  { 
    id: 7, 
    locationCode: "OLD-WH", 
    locationName: "Old Storage", 
    locationType: "Warehouse",
    parentLocation: null,
    address: "999 Old Road, City",
    capacity: "3000 sq.ft",
    isActive: false,
    manager: "N/A"
  },
];

export default function LocationsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("All");
  const [statusFilter, setStatusFilter] = useState("All");

  const filteredData = sampleLocationData.filter(location => {
    const matchesSearch = location.locationName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         location.locationCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         location.address.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === "All" || location.locationType === typeFilter;
    const matchesStatus = statusFilter === "All" || 
                         (statusFilter === "Active" && location.isActive) ||
                         (statusFilter === "Inactive" && !location.isActive);
    return matchesSearch && matchesType && matchesStatus;
  });

  const handleDelete = (id: number) => {
    // In a real app, this would make an API call
    alert(`Delete location with ID: ${id}`);
  };

  const locationTypes = [...new Set(sampleLocationData.map(item => item.locationType))];

  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'Warehouse': return Warehouse;
      case 'Production': return Building;
      case 'Quality Control': return Building;
      default: return MapPin;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Storage Locations</h1>
            <p className="text-gray-600">
              Manage warehouses, storage areas, and other locations for inventory management
            </p>
          </div>
          <Link href="/masters/inventory/locations/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Location
            </Button>
          </Link>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <MapPin className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Locations</p>
                  <p className="text-2xl font-bold">{sampleLocationData.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Warehouse className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Warehouses</p>
                  <p className="text-2xl font-bold">
                    {sampleLocationData.filter(loc => loc.locationType === "Warehouse").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Building className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Production Areas</p>
                  <p className="text-2xl font-bold">
                    {sampleLocationData.filter(loc => loc.locationType === "Production").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <MapPin className="h-8 w-8 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Locations</p>
                  <p className="text-2xl font-bold">
                    {sampleLocationData.filter(loc => loc.isActive).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search locations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Type: {typeFilter}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setTypeFilter("All")}>All</DropdownMenuItem>
                  {locationTypes.map((type) => (
                    <DropdownMenuItem key={type} onClick={() => setTypeFilter(type)}>
                      {type}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Status: {statusFilter}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setStatusFilter("All")}>All</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("Active")}>Active</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("Inactive")}>Inactive</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Export to Excel</DropdownMenuItem>
                  <DropdownMenuItem>Import from Excel</DropdownMenuItem>
                  <DropdownMenuItem>Generate Location Map</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card className="frappe-table">
          <CardHeader>
            <CardTitle>Locations List ({filteredData.length} records)</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Location Code</TableHead>
                  <TableHead>Location Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Parent Location</TableHead>
                  <TableHead>Capacity</TableHead>
                  <TableHead>Manager</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((location) => {
                  const LocationIcon = getLocationIcon(location.locationType);
                  return (
                    <TableRow key={location.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <LocationIcon className="h-4 w-4 text-gray-500" />
                          {location.locationCode}
                        </div>
                      </TableCell>
                      <TableCell>{location.locationName}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          location.locationType === 'Warehouse' ? 'bg-blue-100 text-blue-800' :
                          location.locationType === 'Production' ? 'bg-green-100 text-green-800' :
                          location.locationType === 'Section' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {location.locationType}
                        </span>
                      </TableCell>
                      <TableCell>{location.parentLocation || '-'}</TableCell>
                      <TableCell>{location.capacity}</TableCell>
                      <TableCell>{location.manager}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          location.isActive ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50'
                        }`}>
                          {location.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/masters/inventory/locations/${location.id}`}>
                                View Details
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/masters/inventory/locations/${location.id}/edit`}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              View Stock
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              Location History
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDelete(location.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
            
            {filteredData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No locations found. {searchTerm && "Try adjusting your search terms."}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help Section */}
        <Card>
          <CardHeader>
            <CardTitle>Location Management Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Location Hierarchy</h4>
                <p>
                  Organize locations in a hierarchical structure. For example, create main warehouses 
                  and then define sections, racks, or bins within them for better organization.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Best Practices</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li>Use clear, descriptive location codes</li>
                  <li>Assign managers to each location</li>
                  <li>Define capacity limits for better planning</li>
                  <li>Regularly review and update location status</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
