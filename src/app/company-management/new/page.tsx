"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { ArrowLeft, Save, Building2 } from "lucide-react";

export default function NewCompanyPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    companyName: "",
    panNumber: "",
    financialYear: new Date().getFullYear().toString(),
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      alert("Company created successfully!");
      // In real app, redirect to company dashboard or site setup
      router.push("/company-dashboard");
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/company-dashboard">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Add New Company</h1>
              <p className="text-gray-600">Create a new company profile</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Company Information</CardTitle>
            <CardDescription>
              Enter the basic details for your company
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="companyName">
                  Company Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="companyName"
                  name="companyName"
                  type="text"
                  placeholder="Enter your company name"
                  value={formData.companyName}
                  onChange={handleInputChange}
                  required
                />
                <p className="text-sm text-gray-500">
                  Full legal name of your company
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="panNumber">
                  PAN Number <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="panNumber"
                  name="panNumber"
                  type="text"
                  placeholder="**********"
                  value={formData.panNumber}
                  onChange={handleInputChange}
                  pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}"
                  maxLength={10}
                  style={{ textTransform: 'uppercase' }}
                  required
                />
                <p className="text-sm text-gray-500">
                  10-digit PAN number (e.g., **********)
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="financialYear">
                  Financial Year <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="financialYear"
                  name="financialYear"
                  type="text"
                  placeholder="2024"
                  value={formData.financialYear}
                  onChange={handleInputChange}
                  required
                />
                <p className="text-sm text-gray-500">
                  Current financial year (e.g., 2024)
                </p>
              </div>
              
              {/* Form Actions */}
              <div className="flex items-center gap-4 pt-6 border-t">
                <Button type="submit" disabled={isLoading} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  {isLoading ? "Creating Company..." : "Create Company"}
                </Button>
                <Link href="/company-dashboard" className="flex-1">
                  <Button variant="outline" type="button" className="w-full">
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Help Section */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="text-lg">Next Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">1</div>
                <div>
                  <strong>Create Company:</strong> Fill in the company details above
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-gray-100 text-gray-600 rounded-full flex items-center justify-center text-xs font-medium">2</div>
                <div>
                  <strong>Add Sites:</strong> After creating the company, you'll be able to add sites/locations
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-gray-100 text-gray-600 rounded-full flex items-center justify-center text-xs font-medium">3</div>
                <div>
                  <strong>Start Using:</strong> Select a site and begin using the production management system
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
