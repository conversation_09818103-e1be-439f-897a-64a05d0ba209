import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { 
  Package, 
  Factory, 
  ShoppingCart, 
  Users, 
  Settings, 
  Database,
  ArrowRight,
  TrendingUp
} from "lucide-react";

export default function MastersPage() {
  const masterModules = [
    {
      title: "Inventory Masters",
      description: "Manage items, categories, units of measurement, and storage locations",
      icon: Package,
      href: "/masters/inventory",
      color: "bg-blue-100 text-blue-600",
      features: ["Items", "Categories", "UoM", "Locations", "Attributes"],
      count: "1,234 items",
    },
    {
      title: "Manufacturing Masters",
      description: "Configure BOMs, work centers, operations, and manufacturing processes",
      icon: Factory,
      href: "/masters/manufacturing",
      color: "bg-green-100 text-green-600",
      features: ["BOM", "Work Centers", "Operations", "Routing"],
      count: "156 BOMs",
    },
    {
      title: "Purchase & Sales Masters",
      description: "Manage suppliers, customers, price lists, and trading terms",
      icon: ShoppingCart,
      href: "/masters/purchase-sales",
      color: "bg-purple-100 text-purple-600",
      features: ["Suppliers", "Customers", "Price Lists", "Terms"],
      count: "89 suppliers",
    },
  ];

  const quickStats = [
    { label: "Total Items", value: "1,234", icon: Package, change: "+12%" },
    { label: "Active Suppliers", value: "89", icon: Users, change: "+5%" },
    { label: "BOMs Configured", value: "156", icon: Factory, change: "+8%" },
    { label: "Categories", value: "45", icon: Database, change: "+2%" },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Master Data Management</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Centralized hub for managing all your business master data including inventory, 
            manufacturing, and trading partner information.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStats.map((stat) => (
            <Card key={stat.label} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600 flex items-center mt-1">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      {stat.change}
                    </p>
                  </div>
                  <div className="p-3 bg-gray-100 rounded-lg">
                    <stat.icon className="h-6 w-6 text-gray-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Master Modules */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {masterModules.map((module) => (
            <Card key={module.title} className="hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-4">
                  <div className={`p-3 rounded-lg ${module.color}`}>
                    <module.icon className="h-8 w-8" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">{module.title}</CardTitle>
                    <p className="text-sm text-gray-500">{module.count}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <CardDescription className="text-base">
                  {module.description}
                </CardDescription>
                
                {/* Features List */}
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700">Key Features:</p>
                  <div className="flex flex-wrap gap-2">
                    {module.features.map((feature) => (
                      <span 
                        key={feature}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                <Link href={module.href} className="block">
                  <Button className="w-full group">
                    Manage {module.title.split(' ')[0]}
                    <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Master Data Updates</CardTitle>
            <CardDescription>
              Latest changes to your master data across all modules
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4 p-3 bg-blue-50 rounded-lg">
                <Package className="h-5 w-5 text-blue-600" />
                <div className="flex-1">
                  <p className="font-medium">New item "Steel Rod 12mm" added</p>
                  <p className="text-sm text-gray-600">2 hours ago • Inventory Masters</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-3 bg-green-50 rounded-lg">
                <Factory className="h-5 w-5 text-green-600" />
                <div className="flex-1">
                  <p className="font-medium">BOM updated for "Finished Product A"</p>
                  <p className="text-sm text-gray-600">5 hours ago • Manufacturing Masters</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-3 bg-purple-50 rounded-lg">
                <Users className="h-5 w-5 text-purple-600" />
                <div className="flex-1">
                  <p className="font-medium">New supplier "ABC Materials Ltd" registered</p>
                  <p className="text-sm text-gray-600">1 day ago • Purchase & Sales Masters</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
