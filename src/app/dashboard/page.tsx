import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Package, 
  ShoppingCart, 
  Factory, 
  Users, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react";

export default function DashboardPage() {
  const quickStats = [
    {
      title: "Total Items",
      value: "1,234",
      change: "+12%",
      icon: Package,
      color: "text-blue-600",
    },
    {
      title: "Active Orders",
      value: "56",
      change: "+8%",
      icon: ShoppingCart,
      color: "text-green-600",
    },
    {
      title: "Production Jobs",
      value: "23",
      change: "-3%",
      icon: Factory,
      color: "text-orange-600",
    },
    {
      title: "Suppliers",
      value: "89",
      change: "+5%",
      icon: Users,
      color: "text-purple-600",
    },
  ];

  const shortcuts = [
    { title: "Add New Item", href: "/masters/inventory/items/new", icon: Package },
    { title: "Create Purchase Order", href: "/purchase/orders/new", icon: ShoppingCart },
    { title: "New Production Order", href: "/manufacturing/orders/new", icon: Factory },
    { title: "Quality Inspection", href: "/quality/inspections/new", icon: CheckCircle },
  ];

  const recentActivities = [
    { title: "New purchase order PO-2024-001 created", time: "2 hours ago", type: "purchase" },
    { title: "Item ABC123 stock updated", time: "4 hours ago", type: "inventory" },
    { title: "Production order MO-2024-005 completed", time: "6 hours ago", type: "manufacturing" },
    { title: "Quality inspection QI-2024-012 approved", time: "8 hours ago", type: "quality" },
  ];

  return (
    <DashboardLayout>
      <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--sap-spacing-xl)' }}>
        {/* Page Header */}
        <div className="sap-header">
          <h1 style={{
            fontSize: 'var(--sap-font-size-xl)',
            fontWeight: 'bold',
            color: 'var(--sap-text)',
            margin: '0 0 4px 0'
          }}>
            Dashboard
          </h1>
          <p style={{
            fontSize: 'var(--sap-font-size-sm)',
            color: 'var(--sap-text-secondary)',
            margin: '0'
          }}>
            Welcome back! Here's what's happening in your production system.
          </p>
        </div>

        {/* Quick Stats */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: 'var(--sap-spacing-lg)'
        }}>
          {quickStats.map((stat) => (
            <Card key={stat.title}>
              <CardContent>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <p style={{
                      fontSize: 'var(--sap-font-size-sm)',
                      fontWeight: 'bold',
                      color: 'var(--sap-text-secondary)',
                      margin: '0 0 4px 0'
                    }}>
                      {stat.title}
                    </p>
                    <p style={{
                      fontSize: 'var(--sap-font-size-xl)',
                      fontWeight: 'bold',
                      color: 'var(--sap-text)',
                      margin: '0 0 4px 0'
                    }}>
                      {stat.value}
                    </p>
                    <p style={{
                      fontSize: 'var(--sap-font-size-xs)',
                      color: stat.change.startsWith('+') ? 'var(--sap-success)' : 'var(--sap-error)',
                      margin: '0'
                    }}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <stat.icon style={{
                    width: '24px',
                    height: '24px',
                    color: 'var(--sap-text-secondary)'
                  }} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: 'var(--sap-spacing-xl)'
        }}>
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Frequently used features for faster access</CardDescription>
            </CardHeader>
            <CardContent>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gap: 'var(--sap-spacing-md)'
              }}>
                {shortcuts.map((shortcut) => (
                  <Button
                    key={shortcut.title}
                    variant="outline"
                    style={{
                      height: 'auto',
                      padding: 'var(--sap-spacing-lg)',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 'var(--sap-spacing-sm)',
                      textDecoration: 'none'
                    }}
                    asChild
                  >
                    <a href={shortcut.href}>
                      <shortcut.icon style={{ width: '16px', height: '16px' }} />
                      <span style={{
                        fontSize: 'var(--sap-font-size-xs)',
                        textAlign: 'center'
                      }}>
                        {shortcut.title}
                      </span>
                    </a>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activities</CardTitle>
              <CardDescription>Latest updates from your production system</CardDescription>
            </CardHeader>
            <CardContent>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--sap-spacing-md)' }}>
                {recentActivities.map((activity, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: 'var(--sap-spacing-sm)',
                    padding: 'var(--sap-spacing-sm)',
                    borderBottom: '1px solid var(--sap-border)'
                  }}>
                    <div style={{
                      width: '6px',
                      height: '6px',
                      backgroundColor: 'var(--sap-primary)',
                      marginTop: '6px'
                    }}></div>
                    <div style={{ flex: 1 }}>
                      <p style={{
                        fontSize: 'var(--sap-font-size-sm)',
                        color: 'var(--sap-text)',
                        margin: '0 0 2px 0'
                      }}>
                        {activity.title}
                      </p>
                      <p style={{
                        fontSize: 'var(--sap-font-size-xs)',
                        color: 'var(--sap-text-muted)',
                        margin: '0'
                      }}>
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Reports Panel */}
        <Card>
          <CardHeader>
            <CardTitle>Reports & Analytics</CardTitle>
            <CardDescription>Access various reports and analytics</CardDescription>
          </CardHeader>
          <CardContent>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
              gap: 'var(--sap-spacing-md)'
            }}>
              <Button
                variant="outline"
                style={{
                  height: 'auto',
                  padding: 'var(--sap-spacing-lg)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 'var(--sap-spacing-sm)'
                }}
              >
                <TrendingUp style={{ width: '16px', height: '16px' }} />
                <span style={{ fontSize: 'var(--sap-font-size-xs)' }}>Inventory Report</span>
              </Button>
              <Button
                variant="outline"
                style={{
                  height: 'auto',
                  padding: 'var(--sap-spacing-lg)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 'var(--sap-spacing-sm)'
                }}
              >
                <AlertTriangle style={{ width: '16px', height: '16px' }} />
                <span style={{ fontSize: 'var(--sap-font-size-xs)' }}>Low Stock Alert</span>
              </Button>
              <Button
                variant="outline"
                style={{
                  height: 'auto',
                  padding: 'var(--sap-spacing-lg)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 'var(--sap-spacing-sm)'
                }}
              >
                <Clock style={{ width: '16px', height: '16px' }} />
                <span style={{ fontSize: 'var(--sap-font-size-xs)' }}>Production Schedule</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
