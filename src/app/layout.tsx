import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Production Management System",
  description: "SaaS Production Management Software with SAP-style UI",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body style={{ fontFamily: 'var(--sap-font-family)' }}>
        {children}
      </body>
    </html>
  );
}
