"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { ArrowLeft, Save } from "lucide-react";

export default function NewUoMPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    unitName: "",
    alias: "",
    uqc: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      // In a real app, you would save to database here
      alert("UoM created successfully!");
      router.push("/masters/inventory/uom");
    }, 1000);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center gap-4">
          <Link href="/masters/inventory/uom">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Add New UoM</h1>
            <p className="text-gray-600">
              Create a new unit of measurement for your inventory items
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="max-w-2xl">
          <Card className="frappe-form">
            <CardHeader>
              <CardTitle>Unit of Measurement Details</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="unitName">
                      Unit Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="unitName"
                      name="unitName"
                      type="text"
                      placeholder="e.g., Kilogram"
                      value={formData.unitName}
                      onChange={handleInputChange}
                      required
                    />
                    <p className="text-sm text-gray-500">
                      Full name of the unit of measurement
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alias">
                      Alias <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="alias"
                      name="alias"
                      type="text"
                      placeholder="e.g., Kg"
                      value={formData.alias}
                      onChange={handleInputChange}
                      required
                    />
                    <p className="text-sm text-gray-500">
                      Short form or abbreviation
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="uqc">
                    UQC (Unit Quantity Code for GST) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="uqc"
                    name="uqc"
                    type="text"
                    placeholder="e.g., KGS"
                    value={formData.uqc}
                    onChange={handleInputChange}
                    required
                  />
                  <p className="text-sm text-gray-500">
                    GST unit quantity code as per government standards
                  </p>
                </div>

                {/* Form Actions */}
                <div className="flex items-center gap-4 pt-6 border-t">
                  <Button type="submit" disabled={isLoading}>
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading ? "Saving..." : "Save UoM"}
                  </Button>
                  <Link href="/masters/inventory/uom">
                    <Button variant="outline" type="button">
                      Cancel
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Help Section */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Help & Guidelines</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm text-gray-600">
                <div>
                  <strong>Unit Name:</strong> Enter the full name of the unit (e.g., Kilogram, Meter, Piece)
                </div>
                <div>
                  <strong>Alias:</strong> Provide a short abbreviation that will be displayed in forms and reports
                </div>
                <div>
                  <strong>UQC Code:</strong> Use the standard GST unit codes. Common examples:
                  <ul className="list-disc list-inside mt-1 ml-4">
                    <li>KGS - Kilograms</li>
                    <li>GMS - Grams</li>
                    <li>MTR - Meters</li>
                    <li>PCS - Pieces</li>
                    <li>LTR - Liters</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
