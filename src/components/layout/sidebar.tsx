"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { 
  Building2, 
  Package, 
  ShoppingCart, 
  Factory, 
  Users, 
  ClipboardCheck, 
  Settings, 
  ChevronDown, 
  ChevronRight,
  Home
} from "lucide-react";

interface SidebarItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: SidebarItem[];
}

const sidebarItems: SidebarItem[] = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home,
  },
  {
    title: "Purchase",
    icon: ShoppingCart,
    children: [
      { title: "Purchase Orders", href: "/purchase/orders", icon: ShoppingCart },
      { title: "Suppliers", href: "/purchase/suppliers", icon: Users },
    ],
  },
  {
    title: "Sales",
    icon: Package,
    children: [
      { title: "Sales Orders", href: "/sales/orders", icon: Package },
      { title: "Customers", href: "/sales/customers", icon: Users },
    ],
  },
  {
    title: "Inventory",
    icon: Package,
    children: [
      { title: "Items", href: "/inventory/items", icon: Package },
      { title: "Stock", href: "/inventory/stock", icon: Package },
    ],
  },
  {
    title: "Manufacturing",
    icon: Factory,
    children: [
      { title: "Production Orders", href: "/manufacturing/orders", icon: Factory },
      { title: "Bill of Materials", href: "/manufacturing/bom", icon: ClipboardCheck },
    ],
  },
  {
    title: "Sub-Contracting",
    icon: Building2,
    children: [
      { title: "Job Work Orders", href: "/subcontracting/orders", icon: Building2 },
      { title: "Job Workers", href: "/subcontracting/workers", icon: Users },
    ],
  },
  {
    title: "Quality",
    icon: ClipboardCheck,
    children: [
      { title: "Quality Inspections", href: "/quality/inspections", icon: ClipboardCheck },
      { title: "Quality Templates", href: "/quality/templates", icon: ClipboardCheck },
    ],
  },
  {
    title: "Masters",
    icon: Settings,
    children: [
      { title: "Inventory Masters", href: "/masters/inventory", icon: Package },
      { title: "Manufacturing Masters", href: "/masters/manufacturing", icon: Factory },
      { title: "Purchase & Sales Masters", href: "/masters/purchase-sales", icon: ShoppingCart },
    ],
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
];

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [companyName, setCompanyName] = useState("Production Management");
  const [siteName, setSiteName] = useState("");

  useEffect(() => {
    // Load company and site names from localStorage
    if (typeof window !== 'undefined') {
      const storedCompanyName = localStorage.getItem('selectedCompanyName');
      const storedSiteName = localStorage.getItem('selectedSiteName');

      if (storedCompanyName) {
        setCompanyName(storedCompanyName);
      }
      if (storedSiteName) {
        setSiteName(storedSiteName);
      }
    }
  }, []);

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  const renderSidebarItem = (item: SidebarItem, level = 0) => {
    const isExpanded = expandedItems.includes(item.title);
    const hasChildren = item.children && item.children.length > 0;
    const isActive = item.href === pathname;

    return (
      <div key={item.title}>
        {item.href ? (
          <Link
            href={item.href}
            className={cn(
              "sap-nav-item flex items-center gap-2",
              isActive && "active",
              level > 0 && "pl-6"
            )}
            style={{
              fontSize: 'var(--sap-font-size-sm)',
              textDecoration: 'none'
            }}
          >
            <item.icon className="h-3 w-3" />
            {item.title}
          </Link>
        ) : (
          <button
            onClick={() => toggleExpanded(item.title)}
            className={cn(
              "sap-nav-item flex w-full items-center gap-2",
              level > 0 && "pl-6"
            )}
            style={{
              fontSize: 'var(--sap-font-size-sm)',
              textAlign: 'left'
            }}
          >
            <item.icon className="h-3 w-3" />
            <span className="flex-1">{item.title}</span>
            {hasChildren && (
              isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )
            )}
          </button>
        )}

        {hasChildren && isExpanded && (
          <div>
            {item.children!.map(child => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("sap-sidebar w-64 h-full", className)}>
      <div className="sap-header">
        <h2 style={{
          fontSize: 'var(--sap-font-size-lg)',
          fontWeight: 'bold',
          color: 'var(--sap-text)',
          margin: '0 0 4px 0'
        }}>
          Production Management
        </h2>
        <p style={{
          fontSize: 'var(--sap-font-size-sm)',
          color: 'var(--sap-text-secondary)',
          margin: '0'
        }}>
          {companyName}
        </p>
        {siteName && (
          <p style={{
            fontSize: 'var(--sap-font-size-xs)',
            color: 'var(--sap-text-muted)',
            margin: '2px 0 0 0'
          }}>
            {siteName}
          </p>
        )}
        <button
          onClick={() => window.location.href = '/company-dashboard'}
          className="sap-button-secondary mt-2 px-2 py-1"
          style={{ fontSize: 'var(--sap-font-size-xs)' }}
        >
          Switch Company/Site
        </button>
      </div>

      <nav>
        {sidebarItems.map(item => renderSidebarItem(item))}
      </nav>
    </div>
  );
}
