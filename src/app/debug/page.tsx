"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function DebugPage() {
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const info = {
        selectedCompanyId: localStorage.getItem('selectedCompanyId'),
        selectedCompanyName: localStorage.getItem('selectedCompanyName'),
        selectedSiteId: localStorage.getItem('selectedSiteId'),
        selectedSiteName: localStorage.getItem('selectedSiteName'),
        currentPath: window.location.pathname,
        userAgent: navigator.userAgent,
      };
      setDebugInfo(info);
    }
  }, []);

  const clearStorage = () => {
    if (typeof window !== 'undefined') {
      localStorage.clear();
      window.location.reload();
    }
  };

  const testCompanyFlow = () => {
    if (typeof window !== 'undefined') {
      // Simulate company selection
      localStorage.setItem('selectedCompanyId', '1');
      localStorage.setItem('selectedCompanyName', 'Test Company');
      window.location.href = '/site-selection';
    }
  };

  const testSiteFlow = () => {
    if (typeof window !== 'undefined') {
      // Simulate site selection
      localStorage.setItem('selectedSiteId', '1');
      localStorage.setItem('selectedSiteName', 'Test Site');
      window.location.href = '/dashboard';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Debug Information</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>LocalStorage Data</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={clearStorage} variant="destructive" className="w-full">
                Clear All Storage
              </Button>
              
              <Button onClick={testCompanyFlow} className="w-full">
                Test Company Selection Flow
              </Button>
              
              <Button onClick={testSiteFlow} className="w-full">
                Test Site Selection Flow
              </Button>
              
              <div className="space-y-2">
                <Button 
                  onClick={() => window.location.href = '/company-dashboard'} 
                  variant="outline" 
                  className="w-full"
                >
                  Go to Company Dashboard
                </Button>
                
                <Button 
                  onClick={() => window.location.href = '/site-selection'} 
                  variant="outline" 
                  className="w-full"
                >
                  Go to Site Selection
                </Button>
                
                <Button 
                  onClick={() => window.location.href = '/dashboard'} 
                  variant="outline" 
                  className="w-full"
                >
                  Go to Main Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Flow Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Test the complete flow:
              </p>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Clear storage and go to homepage</li>
                <li>Login → should redirect to company dashboard</li>
                <li>Select a company → should redirect to site selection</li>
                <li>Select a site → should redirect to main dashboard</li>
                <li>Check if company/site names appear in sidebar</li>
              </ol>
              
              <div className="flex gap-2">
                <Button 
                  onClick={() => {
                    clearStorage();
                    window.location.href = '/';
                  }}
                  className="flex-1"
                >
                  Start Fresh Test
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
