"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Plus, Search, MoreHorizontal, Edit, Trash2, MapPin, ArrowLeft, Eye } from "lucide-react";

// Sample data - all sites across all companies
const sampleSites = [
  { id: 1, siteName: "Main Manufacturing Plant", gstNumber: "27ABCDE1234F1Z5", companyName: "Acme Manufacturing Co.", state: "Maharashtra", address: "Plot No. 123, Industrial Area, Pune", pincode: "411001", isDefault: true, isActive: true },
  { id: 2, siteName: "Warehouse - Mumbai", gstNumber: "27ABCDE1234F2Z6", companyName: "Acme Manufacturing Co.", state: "Maharashtra", address: "Godown No. 45, MIDC, Mumbai", pincode: "400001", isDefault: false, isActive: true },
  { id: 3, siteName: "Tech Center", gstNumber: "27FGHIJ5678K1Z5", companyName: "Tech Solutions Ltd.", state: "Karnataka", address: "Tech Park, Bangalore", pincode: "560001", isDefault: true, isActive: true },
  { id: 4, siteName: "Production Unit 1", gstNumber: "27KLMNO9012P1Z5", companyName: "Global Industries Pvt Ltd", state: "Gujarat", address: "Industrial Estate, Ahmedabad", pincode: "380001", isDefault: true, isActive: true },
  { id: 5, siteName: "Production Unit 2", gstNumber: "27KLMNO9012P2Z6", companyName: "Global Industries Pvt Ltd", state: "Gujarat", address: "GIDC, Vadodara", pincode: "390001", isDefault: false, isActive: true },
];

export default function SiteManagementPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [sites, setSites] = useState(sampleSites);

  const filteredData = sites.filter(
    (site) =>
      site.siteName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      site.gstNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      site.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      site.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = (id: number) => {
    if (confirm("Are you sure you want to delete this site?")) {
      setSites(sites.filter(site => site.id !== id));
    }
  };

  const toggleStatus = (id: number) => {
    setSites(sites.map(site => 
      site.id === id 
        ? { ...site, isActive: !site.isActive }
        : site
    ));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/dashboard">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div className="flex items-center gap-3 flex-1">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MapPin className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Site Management</h1>
              <p className="text-gray-600">Manage all sites across all companies</p>
            </div>
          </div>
          <Link href="/site-management/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Site
            </Button>
          </Link>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search sites..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Export to Excel</DropdownMenuItem>
                  <DropdownMenuItem>Import Sites</DropdownMenuItem>
                  <DropdownMenuItem>Bulk Operations</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Sites Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Sites ({filteredData.length} records)</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Site Name</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>GST Number</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Default</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((site) => (
                  <TableRow key={site.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-blue-600" />
                        {site.siteName}
                      </div>
                    </TableCell>
                    <TableCell>{site.companyName}</TableCell>
                    <TableCell>
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {site.gstNumber}
                      </code>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <p className="font-medium">{site.address}</p>
                        <p className="text-gray-500">{site.state} - {site.pincode}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        site.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {site.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </TableCell>
                    <TableCell>
                      {site.isDefault && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Default
                        </span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/site-management/${site.id}`}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/site-management/${site.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Site
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => toggleStatus(site.id)}
                          >
                            {site.isActive ? 'Deactivate' : 'Activate'}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(site.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No sites found. {searchTerm && "Try adjusting your search terms."}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
