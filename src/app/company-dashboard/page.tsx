"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { SiteSelectionPopup } from "@/components/site-selection-popup";
import Link from "next/link";
import { Building2, Plus, Settings, Users, MapPin } from "lucide-react";

// Sample company data - in real app, this would come from API
const sampleCompanies = [
  {
    id: 1,
    name: "Acme Manufacturing Co.",
    panNumber: "**********",
    financialYear: "2024",
    sitesCount: 3,
    isActive: true,
  },
  {
    id: 2,
    name: "Tech Solutions Ltd.",
    panNumber: "**********",
    financialYear: "2024",
    sitesCount: 1,
    isActive: false,
  },
];

export default function CompanyDashboardPage() {
  const [companies] = useState(sampleCompanies);
  const [selectedCompany, setSelectedCompany] = useState<number | null>(null);
  const [showSitePopup, setShowSitePopup] = useState(false);
  const [selectedCompanyForSite, setSelectedCompanyForSite] = useState<{id: number, name: string} | null>(null);

  const handleCompanySelect = (companyId: number) => {
    try {
      console.log('Selecting company with ID:', companyId);
      setSelectedCompany(companyId);

      // Store company data in localStorage
      if (typeof window !== 'undefined') {
        const selectedCompanyData = companies.find(company => company.id === companyId);
        console.log('Selected company data:', selectedCompanyData);

        if (selectedCompanyData) {
          localStorage.setItem('selectedCompanyId', companyId.toString());
          localStorage.setItem('selectedCompanyName', selectedCompanyData.name);

          console.log('Stored company ID:', companyId);
          console.log('Stored company name:', selectedCompanyData.name);
          console.log('Opening site selection popup...');

          // Show site selection popup instead of redirecting
          setSelectedCompanyForSite({
            id: companyId,
            name: selectedCompanyData.name
          });
          setShowSitePopup(true);
        } else {
          console.error('Company not found for ID:', companyId);
          alert('Company not found. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error selecting company:', error);
      alert('Error selecting company. Please try again.');
    }
  };

  const handleSiteSelect = (siteId: number, siteName: string) => {
    try {
      console.log('Selecting site with ID:', siteId, 'Name:', siteName);

      // Store selected site in localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('selectedSiteId', siteId.toString());
        localStorage.setItem('selectedSiteName', siteName);

        console.log('Stored site ID:', siteId);
        console.log('Stored site name:', siteName);
        console.log('Redirecting to dashboard...');

        // Redirect to dashboard
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 100);
      }
    } catch (error) {
      console.error('Error selecting site:', error);
      alert('Error selecting site. Please try again.');
    }
  };

  const handleCloseSitePopup = () => {
    setShowSitePopup(false);
    setSelectedCompanyForSite(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to Production Management System
          </h1>
          <p className="text-gray-600">
            Select a company to continue or add a new company
          </p>
        </div>

        {/* Companies Grid */}
        {companies.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {companies.map((company) => (
              <Card
                key={company.id}
                className={`transition-all hover:shadow-lg ${
                  company.isActive ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'
                } ${selectedCompany === company.id ? 'ring-2 ring-blue-500' : ''}`}
                onClick={() => {
                  if (company.isActive) {
                    handleCompanySelect(company.id);
                  }
                }}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Building2 className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg">{company.name}</CardTitle>
                      <p className="text-sm text-gray-500">PAN: {company.panNumber}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Financial Year:</span>
                      <span className="font-medium">{company.financialYear}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Sites:</span>
                      <span className="font-medium">{company.sitesCount}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Status:</span>
                      <span className={`font-medium ${
                        company.isActive ? 'text-green-600' : 'text-gray-500'
                      }`}>
                        {company.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Button 
                      className="w-full" 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCompanySelect(company.id);
                      }}
                      disabled={!company.isActive}
                    >
                      {company.isActive ? 'Select Company' : 'Company Inactive'}
                    </Button>
                    <div className="flex gap-2">
                      <Link href={`/company-management/${company.id}/edit`} className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          <Settings className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </Link>
                      <Link href={`/company-management/${company.id}/sites`} className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          <MapPin className="h-4 w-4 mr-1" />
                          Sites
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="text-center py-12 mb-8">
            <CardContent>
              <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <CardTitle className="text-xl mb-2">No Companies Found</CardTitle>
              <CardDescription className="mb-6">
                You haven't added any companies yet. Add your first company to get started.
              </CardDescription>
              <Link href="/company-management/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Company
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex justify-center gap-4">
          <Link href="/company-management/new">
            <Button variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add New Company
            </Button>
          </Link>
          <Link href="/company-management">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Manage Companies
            </Button>
          </Link>
        </div>

        {/* User Actions */}
        <div className="mt-8 text-center">
          <Link href="/profile" className="text-sm text-gray-600 hover:underline mr-4">
            Profile Settings
          </Link>
          <button 
            onClick={() => {
              localStorage.clear();
              window.location.href = '/';
            }}
            className="text-sm text-gray-600 hover:underline"
          >
            Logout
          </button>
        </div>
      </div>

      {/* Site Selection Popup */}
      {selectedCompanyForSite && (
        <SiteSelectionPopup
          isOpen={showSitePopup}
          onClose={handleCloseSitePopup}
          companyId={selectedCompanyForSite.id}
          companyName={selectedCompanyForSite.name}
          onSiteSelect={handleSiteSelect}
        />
      )}
    </div>
  );
}
