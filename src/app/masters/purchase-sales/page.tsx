import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { 
  ShoppingCart, 
  Users, 
  DollarSign, 
  FileText, 
  Truck,
  CreditCard,
  Building,
  Package
} from "lucide-react";

export default function PurchaseSalesMastersPage() {
  const purchaseSalesMasters = [
    {
      title: "Suppliers",
      description: "Manage vendor information and supplier relationships",
      icon: Building,
      href: "/masters/purchase-sales/suppliers",
      count: "45 suppliers",
    },
    {
      title: "Customers",
      description: "Maintain customer database and contact information",
      icon: Users,
      href: "/masters/purchase-sales/customers",
      count: "128 customers",
    },
    {
      title: "Price Lists",
      description: "Configure pricing structures for items and services",
      icon: DollarSign,
      href: "/masters/purchase-sales/price-lists",
      count: "8 price lists",
    },
    {
      title: "Payment Terms",
      description: "Define payment conditions and credit terms",
      icon: CreditCard,
      href: "/masters/purchase-sales/payment-terms",
      count: "12 terms",
    },
    {
      title: "Shipping Rules",
      description: "Set up shipping methods and delivery conditions",
      icon: Truck,
      href: "/masters/purchase-sales/shipping-rules",
      count: "6 shipping rules",
    },
    {
      title: "Tax Templates",
      description: "Configure tax calculations and templates",
      icon: FileText,
      href: "/masters/purchase-sales/tax-templates",
      count: "15 templates",
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Purchase & Sales Masters</h1>
          <p className="text-gray-600">
            Configure all purchase and sales related master data including suppliers, customers, pricing, and terms.
          </p>
        </div>

        {/* Masters Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {purchaseSalesMasters.map((master) => (
            <Card key={master.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <master.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{master.title}</CardTitle>
                    <p className="text-sm text-gray-500">{master.count}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="mb-4">
                  {master.description}
                </CardDescription>
                <Link href={master.href}>
                  <Button className="w-full">
                    Manage {master.title}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks for purchase and sales management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link href="/masters/purchase-sales/suppliers/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <Building className="h-6 w-6" />
                  <span className="text-sm">Add Supplier</span>
                </Button>
              </Link>
              <Link href="/masters/purchase-sales/customers/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <Users className="h-6 w-6" />
                  <span className="text-sm">Add Customer</span>
                </Button>
              </Link>
              <Link href="/masters/purchase-sales/price-lists/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <DollarSign className="h-6 w-6" />
                  <span className="text-sm">Create Price List</span>
                </Button>
              </Link>
              <Link href="/masters/purchase-sales/payment-terms/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <CreditCard className="h-6 w-6" />
                  <span className="text-sm">Add Payment Terms</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Business Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Supplier Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Top Suppliers</CardTitle>
              <CardDescription>
                Best performing suppliers by volume and reliability
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">ABC Manufacturing Ltd</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">₹2.5M</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '95%' }}></div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">XYZ Components Inc</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">₹1.8M</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '88%' }}></div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">Steel Works Co</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">₹1.2M</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-600 h-2 rounded-full" style={{ width: '82%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Top Customers</CardTitle>
              <CardDescription>
                Highest value customers by revenue contribution
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Global Industries Ltd</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">₹3.2M</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '98%' }}></div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Tech Solutions Corp</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">₹2.1M</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">Manufacturing Hub</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">₹1.7M</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-600 h-2 rounded-full" style={{ width: '78%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Purchase & Sales Activities</CardTitle>
            <CardDescription>
              Latest updates to purchase and sales master data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4 p-3 bg-blue-50 rounded-lg">
                <Building className="h-5 w-5 text-blue-600" />
                <div className="flex-1">
                  <p className="font-medium">New supplier "Advanced Materials Ltd" added</p>
                  <p className="text-sm text-gray-600">3 hours ago • Supplier Management</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-3 bg-green-50 rounded-lg">
                <Users className="h-5 w-5 text-green-600" />
                <div className="flex-1">
                  <p className="font-medium">Customer "Retail Chain Co" credit limit updated</p>
                  <p className="text-sm text-gray-600">6 hours ago • Customer Management</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-3 bg-purple-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-purple-600" />
                <div className="flex-1">
                  <p className="font-medium">Price list "Wholesale 2024" activated</p>
                  <p className="text-sm text-gray-600">1 day ago • Price Management</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-3 bg-orange-50 rounded-lg">
                <CreditCard className="h-5 w-5 text-orange-600" />
                <div className="flex-1">
                  <p className="font-medium">Payment terms "Net 45 Days" created</p>
                  <p className="text-sm text-gray-600">2 days ago • Terms Management</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Purchase & Sales Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Purchase & Sales Metrics</CardTitle>
            <CardDescription>
              Key performance indicators for purchase and sales operations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">45</div>
                <div className="text-sm text-gray-600">Active Suppliers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">128</div>
                <div className="text-sm text-gray-600">Active Customers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">8</div>
                <div className="text-sm text-gray-600">Price Lists</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">₹12.5M</div>
                <div className="text-sm text-gray-600">Monthly Volume</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
