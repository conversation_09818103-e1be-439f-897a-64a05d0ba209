import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { 
  Factory, 
  Cog, 
  GitBranch, 
  Route, 
  Settings, 
  Clock,
  Users,
  Wrench
} from "lucide-react";

export default function ManufacturingMastersPage() {
  const manufacturingMasters = [
    {
      title: "Bill of Materials (BOM)",
      description: "Define product structures and component requirements",
      icon: GitBranch,
      href: "/masters/manufacturing/bom",
      count: "156 BOMs",
    },
    {
      title: "Work Centers",
      description: "Configure production workstations and their capabilities",
      icon: Factory,
      href: "/masters/manufacturing/work-centers",
      count: "12 work centers",
    },
    {
      title: "Operations",
      description: "Define manufacturing operations and processes",
      icon: Cog,
      href: "/masters/manufacturing/operations",
      count: "45 operations",
    },
    {
      title: "Routing",
      description: "Set up production sequences and workflows",
      icon: Route,
      href: "/masters/manufacturing/routing",
      count: "89 routings",
    },
    {
      title: "Machine Master",
      description: "Manage production machines and equipment",
      icon: Settings,
      href: "/masters/manufacturing/machines",
      count: "28 machines",
    },
    {
      title: "Shift Master",
      description: "Configure production shifts and schedules",
      icon: Clock,
      href: "/masters/manufacturing/shifts",
      count: "3 shifts",
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Manufacturing Masters</h1>
          <p className="text-gray-600">
            Configure all manufacturing-related master data including BOMs, work centers, operations, and routing.
          </p>
        </div>

        {/* Masters Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {manufacturingMasters.map((master) => (
            <Card key={master.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <master.icon className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{master.title}</CardTitle>
                    <p className="text-sm text-gray-500">{master.count}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="mb-4">
                  {master.description}
                </CardDescription>
                <Link href={master.href}>
                  <Button className="w-full">
                    Manage {master.title.split(' ')[0]}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks for manufacturing management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link href="/masters/manufacturing/bom/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <GitBranch className="h-6 w-6" />
                  <span className="text-sm">Create BOM</span>
                </Button>
              </Link>
              <Link href="/masters/manufacturing/work-centers/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <Factory className="h-6 w-6" />
                  <span className="text-sm">Add Work Center</span>
                </Button>
              </Link>
              <Link href="/masters/manufacturing/operations/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <Cog className="h-6 w-6" />
                  <span className="text-sm">Add Operation</span>
                </Button>
              </Link>
              <Link href="/masters/manufacturing/routing/new">
                <Button variant="outline" className="w-full h-auto p-4 flex flex-col items-center gap-2">
                  <Route className="h-6 w-6" />
                  <span className="text-sm">Create Routing</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Manufacturing Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Production Capacity */}
          <Card>
            <CardHeader>
              <CardTitle>Production Capacity Overview</CardTitle>
              <CardDescription>
                Current capacity utilization across work centers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Factory className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Assembly Line 1</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                    <span className="text-sm text-gray-600">85%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Factory className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Assembly Line 2</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '72%' }}></div>
                    </div>
                    <span className="text-sm text-gray-600">72%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Wrench className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">Machining Center</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-600 h-2 rounded-full" style={{ width: '91%' }}></div>
                    </div>
                    <span className="text-sm text-gray-600">91%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Settings className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-medium">Quality Control</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div className="bg-orange-600 h-2 rounded-full" style={{ width: '45%' }}></div>
                    </div>
                    <span className="text-sm text-gray-600">45%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Manufacturing Activities */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Manufacturing Updates</CardTitle>
              <CardDescription>
                Latest changes to manufacturing master data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-3 bg-green-50 rounded-lg">
                  <GitBranch className="h-5 w-5 text-green-600" />
                  <div className="flex-1">
                    <p className="font-medium">BOM updated for "Product XYZ"</p>
                    <p className="text-sm text-gray-600">2 hours ago • BOM Management</p>
                  </div>
                </div>
                <div className="flex items-center gap-4 p-3 bg-blue-50 rounded-lg">
                  <Factory className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="font-medium">New work center "Assembly Line 3" added</p>
                    <p className="text-sm text-gray-600">5 hours ago • Work Centers</p>
                  </div>
                </div>
                <div className="flex items-center gap-4 p-3 bg-purple-50 rounded-lg">
                  <Route className="h-5 w-5 text-purple-600" />
                  <div className="flex-1">
                    <p className="font-medium">Routing optimized for "Process ABC"</p>
                    <p className="text-sm text-gray-600">1 day ago • Routing Management</p>
                  </div>
                </div>
                <div className="flex items-center gap-4 p-3 bg-orange-50 rounded-lg">
                  <Cog className="h-5 w-5 text-orange-600" />
                  <div className="flex-1">
                    <p className="font-medium">Operation "Welding" parameters updated</p>
                    <p className="text-sm text-gray-600">2 days ago • Operations</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Manufacturing Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Manufacturing Metrics</CardTitle>
            <CardDescription>
              Key performance indicators for manufacturing operations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">156</div>
                <div className="text-sm text-gray-600">Active BOMs</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">12</div>
                <div className="text-sm text-gray-600">Work Centers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">89</div>
                <div className="text-sm text-gray-600">Routing Sequences</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">78%</div>
                <div className="text-sm text-gray-600">Avg. Capacity Utilization</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
