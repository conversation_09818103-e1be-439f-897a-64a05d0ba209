"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Plus, Search, MoreHorizontal, Edit, Trash2, Settings, Tag } from "lucide-react";

// Sample data - in a real app, this would come from an API
const sampleAttributeData = [
  { 
    id: 1, 
    attributeName: "Color", 
    attributeType: "Select", 
    possibleValues: "Red, Blue, Green, Yellow, Black, White",
    isRequired: true,
    applicableCategories: "Finished Goods, Raw Materials",
    description: "Color specification for items"
  },
  { 
    id: 2, 
    attributeName: "Size", 
    attributeType: "Text", 
    possibleValues: "",
    isRequired: false,
    applicableCategories: "All Categories",
    description: "Size dimensions or specifications"
  },
  { 
    id: 3, 
    attributeName: "Material Grade", 
    attributeType: "Select", 
    possibleValues: "Grade A, Grade B, Grade C, Premium",
    isRequired: true,
    applicableCategories: "Raw Materials",
    description: "Quality grade of the material"
  },
  { 
    id: 4, 
    attributeName: "Warranty Period", 
    attributeType: "Number", 
    possibleValues: "",
    isRequired: false,
    applicableCategories: "Finished Goods",
    description: "Warranty period in months"
  },
  { 
    id: 5, 
    attributeName: "Temperature Rating", 
    attributeType: "Number", 
    possibleValues: "",
    isRequired: false,
    applicableCategories: "Electronic Components",
    description: "Operating temperature range"
  },
  { 
    id: 6, 
    attributeName: "Certification", 
    attributeType: "Multi-Select", 
    possibleValues: "ISO 9001, CE, RoHS, FCC, UL",
    isRequired: false,
    applicableCategories: "All Categories",
    description: "Quality and compliance certifications"
  },
];

export default function ItemAttributesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("All");

  const filteredData = sampleAttributeData.filter(attribute => {
    const matchesSearch = attribute.attributeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         attribute.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         attribute.applicableCategories.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === "All" || attribute.attributeType === typeFilter;
    return matchesSearch && matchesType;
  });

  const handleDelete = (id: number) => {
    // In a real app, this would make an API call
    alert(`Delete attribute with ID: ${id}`);
  };

  const attributeTypes = [...new Set(sampleAttributeData.map(item => item.attributeType))];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Item Attributes</h1>
            <p className="text-gray-600">
              Define custom attributes to capture additional item properties and specifications
            </p>
          </div>
          <Link href="/masters/inventory/attributes/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Attribute
            </Button>
          </Link>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Settings className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Attributes</p>
                  <p className="text-2xl font-bold">{sampleAttributeData.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Tag className="h-8 w-8 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">Required Attributes</p>
                  <p className="text-2xl font-bold">
                    {sampleAttributeData.filter(attr => attr.isRequired).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Settings className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Select Type</p>
                  <p className="text-2xl font-bold">
                    {sampleAttributeData.filter(attr => attr.attributeType === "Select").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Settings className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Text Type</p>
                  <p className="text-2xl font-bold">
                    {sampleAttributeData.filter(attr => attr.attributeType === "Text").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search attributes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Type: {typeFilter}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setTypeFilter("All")}>All</DropdownMenuItem>
                  {attributeTypes.map((type) => (
                    <DropdownMenuItem key={type} onClick={() => setTypeFilter(type)}>
                      {type}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Export to Excel</DropdownMenuItem>
                  <DropdownMenuItem>Import from Excel</DropdownMenuItem>
                  <DropdownMenuItem>Bulk Update</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card className="frappe-table">
          <CardHeader>
            <CardTitle>Attributes List ({filteredData.length} records)</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Attribute Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Required</TableHead>
                  <TableHead>Possible Values</TableHead>
                  <TableHead>Applicable Categories</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((attribute) => (
                  <TableRow key={attribute.id}>
                    <TableCell className="font-medium">{attribute.attributeName}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        attribute.attributeType === 'Select' ? 'bg-blue-100 text-blue-800' :
                        attribute.attributeType === 'Text' ? 'bg-green-100 text-green-800' :
                        attribute.attributeType === 'Number' ? 'bg-purple-100 text-purple-800' :
                        'bg-orange-100 text-orange-800'
                      }`}>
                        {attribute.attributeType}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        attribute.isRequired ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {attribute.isRequired ? 'Required' : 'Optional'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate" title={attribute.possibleValues}>
                        {attribute.possibleValues || '-'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate" title={attribute.applicableCategories}>
                        {attribute.applicableCategories}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate" title={attribute.description}>
                        {attribute.description}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/inventory/attributes/${attribute.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/inventory/attributes/${attribute.id}`}>
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            View Usage
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(attribute.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No attributes found. {searchTerm && "Try adjusting your search terms."}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help Section */}
        <Card>
          <CardHeader>
            <CardTitle>About Item Attributes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Attribute Types</h4>
                <ul className="space-y-1">
                  <li><strong>Text:</strong> Free-form text input</li>
                  <li><strong>Number:</strong> Numeric values only</li>
                  <li><strong>Select:</strong> Single choice from predefined options</li>
                  <li><strong>Multi-Select:</strong> Multiple choices from predefined options</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Usage Tips</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li>Use attributes to capture item-specific properties</li>
                  <li>Mark critical attributes as required</li>
                  <li>Define possible values for consistency</li>
                  <li>Apply attributes to relevant categories only</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
