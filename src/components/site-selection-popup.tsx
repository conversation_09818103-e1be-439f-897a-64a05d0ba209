"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Building2, MapPin, Plus, Settings } from "lucide-react";
import Link from "next/link";

// Sample site data - in real app, this would come from API based on selected company
const sampleSites = [
  {
    id: 1,
    companyId: 1,
    siteName: "Main Manufacturing Plant",
    gstNumber: "27ABCDE1234F1Z5",
    country: "India",
    state: "Maharashtra",
    address: "Plot No. 123, Industrial Area, Pune",
    pincode: "411001",
    isDefault: true,
    isActive: true,
  },
  {
    id: 2,
    companyId: 1,
    siteName: "Warehouse - Mumbai",
    gstNumber: "27ABCDE1234F2Z6",
    country: "India", 
    state: "Maharashtra",
    address: "Godown No. 45, MIDC, Mumbai",
    pincode: "400001",
    isDefault: false,
    isActive: true,
  },
  {
    id: 3,
    companyId: 1,
    siteName: "R&D Center",
    gstNumber: "27ABCDE1234F3Z7",
    country: "India",
    state: "Maharashtra", 
    address: "Tech Park, Phase 2, Hinjewadi, Pune",
    pincode: "411057",
    isDefault: false,
    isActive: false,
  },
  {
    id: 4,
    companyId: 2,
    siteName: "Tech Center",
    gstNumber: "27FGHIJ5678K1Z5",
    country: "India",
    state: "Karnataka",
    address: "Tech Park, Bangalore",
    pincode: "560001",
    isDefault: true,
    isActive: true,
  },
];

interface SiteSelectionPopupProps {
  isOpen: boolean;
  onClose: () => void;
  companyId: number;
  companyName: string;
  onSiteSelect: (siteId: number, siteName: string) => void;
}

export function SiteSelectionPopup({ 
  isOpen, 
  onClose, 
  companyId, 
  companyName, 
  onSiteSelect 
}: SiteSelectionPopupProps) {
  const [sites, setSites] = useState<any[]>([]);
  const [selectedSite, setSelectedSite] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isOpen && companyId) {
      setLoading(true);
      // Filter sites for the selected company
      const companySites = sampleSites.filter(site => 
        site.companyId === companyId && site.isActive
      );
      setSites(companySites);
      
      // Auto-select default site if available
      const defaultSite = companySites.find(site => site.isDefault);
      if (defaultSite) {
        setSelectedSite(defaultSite.id);
      }
      
      setLoading(false);
    }
  }, [isOpen, companyId]);

  const handleSiteSelect = (siteId: number) => {
    const selectedSiteData = sites.find(site => site.id === siteId);
    if (selectedSiteData) {
      onSiteSelect(siteId, selectedSiteData.siteName);
      onClose();
    }
  };

  const handleContinue = () => {
    if (selectedSite) {
      handleSiteSelect(selectedSite);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-2 mb-2">
            <Building2 className="h-6 w-6 text-blue-600" />
            <DialogTitle>{companyName}</DialogTitle>
          </div>
          <DialogDescription>
            Select a site to continue your session
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <p style={{ fontSize: 'var(--sap-font-size-sm)', color: 'var(--sap-text-secondary)' }}>
              Loading sites...
            </p>
          </div>
        ) : sites.length > 0 ? (
          <div className="space-y-6">
            {/* Sites Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
              {sites.map((site) => (
                <Card 
                  key={site.id} 
                  className={`cursor-pointer transition-all hover:shadow-lg ${
                    selectedSite === site.id ? 'ring-2 ring-blue-500' : ''
                  } ${site.isDefault ? 'ring-2 ring-green-500' : ''}`}
                  onClick={() => setSelectedSite(site.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <MapPin className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <CardTitle className="text-base">{site.siteName}</CardTitle>
                          {site.isDefault && (
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                              Default
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 mt-1">GST: {site.gstNumber}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-1 text-sm text-gray-600">
                      <p>{site.address}</p>
                      <p>{site.state} - {site.pincode}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-4 border-t">
              <div className="flex gap-2">
                <Link href="/site-management/new">
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add New Site
                  </Button>
                </Link>
                <Link href="/site-management">
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage Sites
                  </Button>
                </Link>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleContinue}
                  disabled={!selectedSite}
                >
                  Continue
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="p-4 bg-gray-50 rounded-lg mb-4">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p style={{ fontSize: 'var(--sap-font-size-sm)', color: 'var(--sap-text-secondary)' }}>
                No active sites found for this company
              </p>
            </div>
            <div className="flex justify-center gap-2">
              <Link href="/site-management/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Site
                </Button>
              </Link>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
