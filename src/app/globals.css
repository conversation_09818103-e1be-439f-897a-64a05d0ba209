@import "tailwindcss";

/* SAP-style CSS Variables */
:root {
  /* SAP Classic Color Palette */
  --sap-background: #f7f7f7;
  --sap-surface: #ffffff;
  --sap-surface-alt: #e8e8e8;
  --sap-border: #c0c0c0;
  --sap-border-dark: #808080;
  --sap-text: #000000;
  --sap-text-secondary: #666666;
  --sap-text-muted: #999999;
  --sap-primary: #0066cc;
  --sap-primary-hover: #004499;
  --sap-secondary: #f0f0f0;
  --sap-secondary-hover: #e0e0e0;
  --sap-accent: #ff6600;
  --sap-success: #008000;
  --sap-warning: #ffcc00;
  --sap-error: #cc0000;

  /* SAP Typography */
  --sap-font-family: 'Segoe UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  --sap-font-mono: 'Courier New', <PERSON>urier, monospace;
  --sap-font-size-xs: 12px;
  --sap-font-size-sm: 14px;
  --sap-font-size-base: 16px;
  --sap-font-size-lg: 18px;
  --sap-font-size-xl: 20px;

  /* SAP Spacing */
  --sap-spacing-xs: 2px;
  --sap-spacing-sm: 4px;
  --sap-spacing-md: 8px;
  --sap-spacing-lg: 12px;
  --sap-spacing-xl: 16px;
  --sap-spacing-2xl: 24px;
}

/* Base SAP Styling */
body {
  font-family: var(--sap-font-family);
  font-size: var(--sap-font-size-base);
  background-color: var(--sap-background);
  color: var(--sap-text);
  line-height: 1.2;
}

/* Remove modern styling */
* {
  box-sizing: border-box;
}

*:focus {
  outline: 1px dotted var(--sap-text);
  outline-offset: 1px;
}

/* SAP-style Components */
.sap-sidebar {
  background-color: var(--sap-surface-alt);
  border-right: 2px solid var(--sap-border);
  font-size: var(--sap-font-size-sm);
}

.sap-main-content {
  background-color: var(--sap-background);
}

.sap-form {
  background-color: var(--sap-surface);
  border: 1px solid var(--sap-border);
  border-radius: 0;
  box-shadow: none;
}

.sap-table {
  background-color: var(--sap-surface);
  border: 1px solid var(--sap-border);
  border-radius: 0;
  box-shadow: none;
}

.sap-button-primary {
  background-color: var(--sap-primary);
  color: white;
  font-weight: normal;
  font-size: var(--sap-font-size-sm);
  padding: var(--sap-spacing-sm) var(--sap-spacing-lg);
  border: 1px solid var(--sap-border-dark);
  border-radius: 0;
  transition: none;
  text-transform: none;
}

.sap-button-primary:hover {
  background-color: var(--sap-primary-hover);
}

.sap-button-secondary {
  background-color: var(--sap-secondary);
  color: var(--sap-text);
  font-weight: normal;
  font-size: var(--sap-font-size-sm);
  padding: var(--sap-spacing-sm) var(--sap-spacing-lg);
  border: 1px solid var(--sap-border);
  border-radius: 0;
  transition: none;
}

.sap-button-secondary:hover {
  background-color: var(--sap-secondary-hover);
}

.sap-input {
  background-color: var(--sap-surface);
  border: 1px solid var(--sap-border);
  border-radius: 0;
  font-family: var(--sap-font-family);
  font-size: var(--sap-font-size-base);
  padding: var(--sap-spacing-sm);
}

.sap-input:focus {
  outline: none;
  border-color: var(--sap-primary);
  box-shadow: none;
}

/* Utility class for inputs containing a left-aligned icon */
.search-input {
  padding-left: 2.5rem !important; /* 40px to clear 16px icon + gap */
}

/* Override for existing Tailwind pl-10 utility when used with sap-input */
.sap-input.pl-10 {
  padding-left: 2.5rem !important;
}

.sap-card {
  background-color: var(--sap-surface);
  border: 1px solid var(--sap-border);
  border-radius: 0;
  box-shadow: none;
}

.sap-header {
  background-color: var(--sap-surface-alt);
  border-bottom: 1px solid var(--sap-border);
  padding: var(--sap-spacing-md);
}

.sap-toolbar {
  background-color: var(--sap-surface-alt);
  border: 1px solid var(--sap-border);
  padding: var(--sap-spacing-sm);
}

/* SAP Table Styling */
.sap-table-header {
  background-color: var(--sap-surface-alt);
  border-bottom: 2px solid var(--sap-border-dark);
  font-weight: bold;
  font-size: var(--sap-font-size-sm);
}

.sap-table-row {
  border-bottom: 1px solid var(--sap-border);
}

.sap-table-row:nth-child(even) {
  background-color: #fafafa;
}

.sap-table-cell {
  padding: var(--sap-spacing-sm);
  font-size: var(--sap-font-size-sm);
  border-right: 1px solid var(--sap-border);
}

/* SAP Navigation */
.sap-nav-item {
  padding: var(--sap-spacing-sm) var(--sap-spacing-md);
  border-bottom: 1px solid var(--sap-border);
  font-size: var(--sap-font-size-sm);
}

.sap-nav-item:hover {
  background-color: var(--sap-secondary-hover);
}

.sap-nav-item.active {
  background-color: var(--sap-primary);
  color: white;
}

/* SAP Status Indicators */
.sap-status-success {
  color: var(--sap-success);
  font-weight: bold;
}

.sap-status-warning {
  color: var(--sap-warning);
  font-weight: bold;
}

.sap-status-error {
  color: var(--sap-error);
  font-weight: bold;
}

/* SAP Form Elements */
.sap-form-group {
  margin-bottom: var(--sap-spacing-lg);
}

.sap-label {
  display: block;
  font-size: var(--sap-font-size-sm);
  font-weight: bold;
  color: var(--sap-text);
  margin-bottom: var(--sap-spacing-xs);
}

.sap-fieldset {
  border: 1px solid var(--sap-border);
  padding: var(--sap-spacing-lg);
  margin: var(--sap-spacing-lg) 0;
  background-color: var(--sap-surface);
}

.sap-legend {
  font-size: var(--sap-font-size-sm);
  font-weight: bold;
  color: var(--sap-text);
  padding: 0 var(--sap-spacing-sm);
  background-color: var(--sap-surface);
}

/* SAP Menu Bar */
.sap-menubar {
  background-color: var(--sap-surface-alt);
  border-bottom: 1px solid var(--sap-border);
  padding: 0;
  font-size: var(--sap-font-size-sm);
}

.sap-menu-item {
  display: inline-block;
  padding: var(--sap-spacing-sm) var(--sap-spacing-md);
  border-right: 1px solid var(--sap-border);
  cursor: pointer;
}

.sap-menu-item:hover {
  background-color: var(--sap-secondary-hover);
}

/* SAP Dialog/Modal */
.sap-dialog {
  background-color: var(--sap-surface);
  border: 2px solid var(--sap-border-dark);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
}

.sap-dialog-title {
  background-color: var(--sap-surface-alt);
  padding: var(--sap-spacing-sm) var(--sap-spacing-md);
  font-size: var(--sap-font-size-sm);
  font-weight: bold;
  border-bottom: 1px solid var(--sap-border);
}

.frappe-button-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium px-4 py-2 rounded-md transition-colors;
}
