"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Plus, Search, MoreHorizontal, Edit, Trash2, GitBranch, Package, Eye } from "lucide-react";

// Sample data - in a real app, this would come from an API
const sampleBOMData = [
  { 
    id: 1, 
    bomCode: "BOM-001", 
    itemName: "Finished Product A", 
    itemCode: "FG-001",
    version: "1.0", 
    status: "Active",
    totalComponents: 8,
    totalCost: 245.50,
    lastUpdated: "2024-01-15"
  },
  { 
    id: 2, 
    bomCode: "BOM-002", 
    itemName: "Assembly Unit B", 
    itemCode: "ASM-002",
    version: "2.1", 
    status: "Active",
    totalComponents: 12,
    totalCost: 189.75,
    lastUpdated: "2024-01-12"
  },
  { 
    id: 3, 
    bomCode: "BOM-003", 
    itemName: "Motor Assembly", 
    itemCode: "MOT-003",
    version: "1.5", 
    status: "Active",
    totalComponents: 15,
    totalCost: 567.25,
    lastUpdated: "2024-01-10"
  },
  { 
    id: 4, 
    bomCode: "BOM-004", 
    itemName: "Control Panel", 
    itemCode: "CP-004",
    version: "1.0", 
    status: "Draft",
    totalComponents: 6,
    totalCost: 123.00,
    lastUpdated: "2024-01-08"
  },
  { 
    id: 5, 
    bomCode: "BOM-005", 
    itemName: "Legacy Product X", 
    itemCode: "LG-005",
    version: "3.0", 
    status: "Inactive",
    totalComponents: 20,
    totalCost: 445.80,
    lastUpdated: "2023-12-20"
  },
];

export default function BOMPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("All");

  const filteredData = sampleBOMData.filter(bom => {
    const matchesSearch = bom.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bom.bomCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bom.itemCode.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "All" || bom.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleDelete = (id: number) => {
    // In a real app, this would make an API call
    alert(`Delete BOM with ID: ${id}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'text-green-600 bg-green-50';
      case 'Draft': return 'text-yellow-600 bg-yellow-50';
      case 'Inactive': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Bill of Materials (BOM)</h1>
            <p className="text-gray-600">
              Manage product structures and component requirements for manufacturing
            </p>
          </div>
          <Link href="/masters/manufacturing/bom/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create BOM
            </Button>
          </Link>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <GitBranch className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total BOMs</p>
                  <p className="text-2xl font-bold">{sampleBOMData.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Package className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Active BOMs</p>
                  <p className="text-2xl font-bold">
                    {sampleBOMData.filter(bom => bom.status === "Active").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Edit className="h-8 w-8 text-yellow-600" />
                <div>
                  <p className="text-sm text-gray-600">Draft BOMs</p>
                  <p className="text-2xl font-bold">
                    {sampleBOMData.filter(bom => bom.status === "Draft").length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Package className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Avg. Components</p>
                  <p className="text-2xl font-bold">
                    {Math.round(sampleBOMData.reduce((sum, bom) => sum + bom.totalComponents, 0) / sampleBOMData.length)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search BOMs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Status: {statusFilter}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setStatusFilter("All")}>All</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("Active")}>Active</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("Draft")}>Draft</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("Inactive")}>Inactive</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Export to Excel</DropdownMenuItem>
                  <DropdownMenuItem>Import from Excel</DropdownMenuItem>
                  <DropdownMenuItem>BOM Comparison</DropdownMenuItem>
                  <DropdownMenuItem>Cost Analysis</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card className="frappe-table">
          <CardHeader>
            <CardTitle>BOM List ({filteredData.length} records)</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>BOM Code</TableHead>
                  <TableHead>Item Name</TableHead>
                  <TableHead>Item Code</TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Components</TableHead>
                  <TableHead>Total Cost</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((bom) => (
                  <TableRow key={bom.id}>
                    <TableCell className="font-medium">{bom.bomCode}</TableCell>
                    <TableCell>{bom.itemName}</TableCell>
                    <TableCell>{bom.itemCode}</TableCell>
                    <TableCell>
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded text-sm">
                        v{bom.version}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Package className="h-4 w-4 text-gray-500" />
                        {bom.totalComponents}
                      </div>
                    </TableCell>
                    <TableCell>₹{bom.totalCost.toFixed(2)}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(bom.status)}`}>
                        {bom.status}
                      </span>
                    </TableCell>
                    <TableCell>{bom.lastUpdated}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/manufacturing/bom/${bom.id}`}>
                              <Eye className="h-4 w-4 mr-2" />
                              View BOM
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/manufacturing/bom/${bom.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            Copy BOM
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            Version History
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            Cost Breakdown
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(bom.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No BOMs found. {searchTerm && "Try adjusting your search terms."}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help Section */}
        <Card>
          <CardHeader>
            <CardTitle>BOM Management Guide</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">BOM Structure</h4>
                <p>
                  A Bill of Materials defines the complete list of raw materials, components, 
                  and assemblies required to manufacture a finished product. It includes quantities, 
                  specifications, and hierarchical relationships.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Best Practices</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li>Keep BOMs updated with latest component specifications</li>
                  <li>Use version control for BOM changes</li>
                  <li>Validate component availability before activation</li>
                  <li>Regular cost analysis and optimization</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
