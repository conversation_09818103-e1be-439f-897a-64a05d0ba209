"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Plus, Search, MoreHorizontal, Edit, Trash2, FolderOpen, Folder } from "lucide-react";

// Sample data with hierarchical structure
const sampleCategoryData = [
  { id: 1, categoryName: "Raw Materials", categoryCode: "RM", isParent: true, parentCategory: null, description: "All raw materials" },
  { id: 2, categoryName: "Metals", categoryCode: "RM-MET", isParent: false, parentCategory: "Raw Materials", description: "Metal raw materials" },
  { id: 3, categoryName: "Plastics", categoryCode: "RM-PLA", isParent: false, parentCategory: "Raw Materials", description: "Plastic raw materials" },
  { id: 4, categoryName: "Finished Goods", categoryCode: "FG", isParent: true, parentCategory: null, description: "All finished products" },
  { id: 5, categoryName: "Electronics", categoryCode: "FG-ELE", isParent: false, parentCategory: "Finished Goods", description: "Electronic products" },
  { id: 6, categoryName: "Mechanical", categoryCode: "FG-MEC", isParent: false, parentCategory: "Finished Goods", description: "Mechanical products" },
  { id: 7, categoryName: "Semi-Finished", categoryCode: "SF", isParent: true, parentCategory: null, description: "Work in progress items" },
  { id: 8, categoryName: "Assemblies", categoryCode: "SF-ASM", isParent: false, parentCategory: "Semi-Finished", description: "Sub-assemblies" },
];

export default function CategoriesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryData, setCategoryData] = useState(sampleCategoryData);

  const filteredData = categoryData.filter(
    (item) =>
      item.categoryName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.categoryCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleDelete = (id: number) => {
    if (confirm("Are you sure you want to delete this category?")) {
      setCategoryData(categoryData.filter(item => item.id !== id));
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Item Categories</h1>
            <p className="text-gray-600">
              Organize your items into hierarchical categories
            </p>
          </div>
          <Link href="/masters/inventory/categories/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </Button>
          </Link>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Export to Excel</DropdownMenuItem>
                  <DropdownMenuItem>Import from Excel</DropdownMenuItem>
                  <DropdownMenuItem>Bulk Delete</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card className="frappe-table">
          <CardHeader>
            <CardTitle>Category List ({filteredData.length} records)</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category Name</TableHead>
                  <TableHead>Category Code</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Parent Category</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {item.isParent ? (
                          <FolderOpen className="h-4 w-4 text-blue-600" />
                        ) : (
                          <Folder className="h-4 w-4 text-gray-400" />
                        )}
                        <span className={item.isParent ? "font-semibold" : ""}>
                          {item.categoryName}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {item.categoryCode}
                      </code>
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        item.isParent 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {item.isParent ? 'Parent' : 'Child'}
                      </span>
                    </TableCell>
                    <TableCell>{item.parentCategory || '-'}</TableCell>
                    <TableCell className="max-w-xs truncate">{item.description}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/inventory/categories/${item.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/inventory/categories/${item.id}`}>
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          {item.isParent && (
                            <DropdownMenuItem asChild>
                              <Link href={`/masters/inventory/categories/new?parent=${item.id}`}>
                                Add Sub-Category
                              </Link>
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem 
                            onClick={() => handleDelete(item.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No category records found. {searchTerm && "Try adjusting your search terms."}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
