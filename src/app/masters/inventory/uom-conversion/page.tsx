"use client";

import { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Plus, Search, MoreHorizontal, Edit, Trash2, ArrowRightLeft } from "lucide-react";

// Sample data - in a real app, this would come from an API
const sampleConversionData = [
  { 
    id: 1, 
    fromUom: "Kilogram", 
    toUom: "Gram", 
    conversionFactor: 1000, 
    description: "1 Kg = 1000 g",
    category: "Weight"
  },
  { 
    id: 2, 
    fromUom: "Meter", 
    toUom: "Centimeter", 
    conversionFactor: 100, 
    description: "1 m = 100 cm",
    category: "Length"
  },
  { 
    id: 3, 
    fromUom: "Liter", 
    toUom: "Milliliter", 
    conversionFactor: 1000, 
    description: "1 L = 1000 ml",
    category: "Volume"
  },
  { 
    id: 4, 
    fromUom: "Dozen", 
    toUom: "Piece", 
    conversionFactor: 12, 
    description: "1 Dozen = 12 Pieces",
    category: "Quantity"
  },
  { 
    id: 5, 
    fromUom: "Box", 
    toUom: "Piece", 
    conversionFactor: 50, 
    description: "1 Box = 50 Pieces",
    category: "Packaging"
  },
  { 
    id: 6, 
    fromUom: "Ton", 
    toUom: "Kilogram", 
    conversionFactor: 1000, 
    description: "1 Ton = 1000 Kg",
    category: "Weight"
  },
];

export default function UoMConversionPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("All");

  const filteredData = sampleConversionData.filter(conversion => {
    const matchesSearch = conversion.fromUom.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         conversion.toUom.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         conversion.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === "All" || conversion.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const handleDelete = (id: number) => {
    // In a real app, this would make an API call
    alert(`Delete conversion with ID: ${id}`);
  };

  const categories = [...new Set(sampleConversionData.map(item => item.category))];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">UoM Conversion Factors</h1>
            <p className="text-gray-600">
              Define conversion rates between different units of measurement
            </p>
          </div>
          <Link href="/masters/inventory/uom-conversion/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Conversion
            </Button>
          </Link>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <ArrowRightLeft className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Conversions</p>
                  <p className="text-2xl font-bold">{sampleConversionData.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          {categories.slice(0, 3).map((category) => (
            <Card key={category}>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <ArrowRightLeft className="h-8 w-8 text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600">{category}</p>
                    <p className="text-2xl font-bold">
                      {sampleConversionData.filter(item => item.category === category).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search conversions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Category: {categoryFilter}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setCategoryFilter("All")}>All</DropdownMenuItem>
                  {categories.map((category) => (
                    <DropdownMenuItem key={category} onClick={() => setCategoryFilter(category)}>
                      {category}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Export to Excel</DropdownMenuItem>
                  <DropdownMenuItem>Import from Excel</DropdownMenuItem>
                  <DropdownMenuItem>Bulk Update</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card className="frappe-table">
          <CardHeader>
            <CardTitle>Conversion Factors ({filteredData.length} records)</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>From UoM</TableHead>
                  <TableHead>To UoM</TableHead>
                  <TableHead>Conversion Factor</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((conversion) => (
                  <TableRow key={conversion.id}>
                    <TableCell className="font-medium">{conversion.fromUom}</TableCell>
                    <TableCell>{conversion.toUom}</TableCell>
                    <TableCell>
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                        {conversion.conversionFactor}
                      </span>
                    </TableCell>
                    <TableCell>{conversion.description}</TableCell>
                    <TableCell>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                        {conversion.category}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/inventory/uom-conversion/${conversion.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/masters/inventory/uom-conversion/${conversion.id}`}>
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            Test Conversion
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(conversion.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredData.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No conversion factors found. {searchTerm && "Try adjusting your search terms."}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help Section */}
        <Card>
          <CardHeader>
            <CardTitle>Understanding UoM Conversions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">How Conversions Work</h4>
                <p>
                  Conversion factors define how many units of the "To UoM" equal one unit of the "From UoM". 
                  For example, if 1 Kg = 1000 g, then the conversion factor is 1000.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Best Practices</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li>Always use the smaller unit as "To UoM" for easier calculations</li>
                  <li>Double-check conversion factors for accuracy</li>
                  <li>Group related conversions by category</li>
                  <li>Test conversions before using in transactions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
