"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { ArrowLeft, Save, Building2 } from "lucide-react";

// Sample company data - in real app, this would come from API
const sampleCompanies = [
  { id: 1, name: "Acme Manufacturing Co.", panNumber: "**********", financialYear: "2024" },
  { id: 2, name: "Tech Solutions Ltd.", panNumber: "**********", financialYear: "2024" },
  { id: 3, name: "Global Industries Pvt Ltd", panNumber: "**********", financialYear: "2024" },
];

export default function EditCompanyPage() {
  const router = useRouter();
  const params = useParams();
  const companyId = parseInt(params.id as string);
  
  const [formData, setFormData] = useState({
    companyName: "",
    panNumber: "",
    financialYear: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);

  useEffect(() => {
    // Load company data
    const company = sampleCompanies.find(c => c.id === companyId);
    if (company) {
      setFormData({
        companyName: company.name,
        panNumber: company.panNumber,
        financialYear: company.financialYear,
      });
    }
    setIsLoadingData(false);
  }, [companyId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      alert("Company updated successfully!");
      router.push("/company-management");
    }, 1000);
  };

  if (isLoadingData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading company data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/company-management">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Edit Company</h1>
              <p className="text-gray-600">Update company information</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Company Information</CardTitle>
            <CardDescription>
              Update the company details below
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="companyName">
                  Company Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="companyName"
                  name="companyName"
                  type="text"
                  placeholder="Enter your company name"
                  value={formData.companyName}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="panNumber">
                  PAN Number <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="panNumber"
                  name="panNumber"
                  type="text"
                  placeholder="**********"
                  value={formData.panNumber}
                  onChange={handleInputChange}
                  pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}"
                  maxLength={10}
                  style={{ textTransform: 'uppercase' }}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="financialYear">
                  Financial Year <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="financialYear"
                  name="financialYear"
                  type="text"
                  placeholder="2024"
                  value={formData.financialYear}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              {/* Form Actions */}
              <div className="flex items-center gap-4 pt-6 border-t">
                <Button type="submit" disabled={isLoading} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  {isLoading ? "Updating..." : "Update Company"}
                </Button>
                <Link href="/company-management" className="flex-1">
                  <Button variant="outline" type="button" className="w-full">
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
